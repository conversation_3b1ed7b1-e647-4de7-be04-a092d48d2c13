import uuid

from sqlalchemy.ext.asyncio import AsyncSession

from app.common.orm.base_async_repository import BaseAsyncRepository
from app.workspace.models.environment import Environment
from app.workspace.types import EnvironmentType


class EnvironmentRepository(BaseAsyncRepository[Environment]):
    def __init__(self, db_session: AsyncSession):
        super().__init__(db_session, Environment)

    async def get_by_org_id_and_type(
        self, org_id: uuid.UUID, env_type: EnvironmentType
    ) -> Environment | None:
        environments = await self._get_by_attrs(organization_id=org_id, type=env_type)
        return environments[0] if environments else None
