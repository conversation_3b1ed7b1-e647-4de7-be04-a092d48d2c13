from typing import Annotated
from uuid import UUID

from fastapi import Depends

from app.auth.dependencies import AuthenticatedUserIdDep
from app.auth.repository import UserRepository
from app.common.oauth.flow_manager import OAuthFlowType
from app.core.config import config
from app.core.database import AsyncDbSessionDep
from app.workspace.exceptions import NoAdminRightsError
from app.workspace.integrations.user_integrations import (
    UserIntegrations,
)
from app.workspace.repositories.environment import EnvironmentRepository
from app.workspace.repositories.integration_config import IntegrationConfigRepository
from app.workspace.repositories.integration_user import IntegrationUserRepository
from app.workspace.repositories.organization import OrganizationRepository
from app.workspace.repositories.organization_member import OrganizationMemberRepository
from app.workspace.repositories.salesforce_field_mapping import (
    SalesforceFieldMappingRepository,
)
from app.workspace.schemas import OrgEnvironment
from app.workspace.services.calendar import CalendarService
from app.workspace.services.crm import CRMService
from app.workspace.services.file import FileService
from app.workspace.services.google_connection import GoogleConnectionService
from app.workspace.services.hubspot_connection import (
    HubSpotConnectionService,
)
from app.workspace.services.integration_config import IntegrationConfigService
from app.workspace.services.integration_list import IntegrationListService
from app.workspace.services.organization import OrganizationService
from app.workspace.services.organization_team import OrganizationTeamService
from app.workspace.services.salesforce_connection import (
    SalesforceConnectionService,
)
from app.workspace.types import EnvironmentType


def get_organization_service(
    db_session: AsyncDbSessionDep,
) -> OrganizationService:
    return OrganizationService(
        db_session=db_session,
        org_repo=OrganizationRepository(db_session),
        env_repo=EnvironmentRepository(db_session),
    )


OrganizationServiceDep = Annotated[
    OrganizationService, Depends(get_organization_service)
]


def get_organization_team_service(
    db_session: AsyncDbSessionDep,
) -> OrganizationTeamService:
    return OrganizationTeamService(
        db_session=db_session,
        org_member_repo=OrganizationMemberRepository(db_session),
        org_repo=OrganizationRepository(db_session),
        user_repo=UserRepository(db_session),
    )


OrganizationTeamServiceDep = Annotated[
    OrganizationTeamService, Depends(get_organization_team_service)
]


def get_integration_cfg_service(
    db_session: AsyncDbSessionDep,
) -> IntegrationConfigService:
    return IntegrationConfigService(
        integration_cfg_repo=IntegrationConfigRepository(db_session),
        integration_user_repo=IntegrationUserRepository(db_session),
    )


IntegrationConfigServiceDep = Annotated[
    IntegrationConfigService, Depends(get_integration_cfg_service)
]


def get_integration_list_service(
    integration_config_service: IntegrationConfigServiceDep,
) -> IntegrationListService:
    return IntegrationListService(integration_config_service)


IntegrationListServiceDep = Annotated[
    IntegrationListService, Depends(get_integration_list_service)
]


async def get_user_org_id(
    user_id: AuthenticatedUserIdDep,
    org_service: OrganizationServiceDep,
) -> UUID:
    org = await org_service.get_user_organization(user_id)

    if org:
        return org.id

    raise RuntimeError(f"No organization found for user {user_id}")


UserOrgIdDep = Annotated[UUID, Depends(get_user_org_id)]


async def require_admin(
    user_id: AuthenticatedUserIdDep,
    org_member_service: OrganizationTeamServiceDep,
) -> None:
    org_member = await org_member_service.get_team_member(user_id)

    if not org_member:
        raise RuntimeError(f"No organization member found for user {user_id}")

    if not org_member.is_admin:
        raise NoAdminRightsError(
            f"User {user_id} is not an admin of organization {org_member.organization_id}"
        )

    return None


async def get_org_member_id(
    user_id: AuthenticatedUserIdDep,
    org_member_service: OrganizationTeamServiceDep,
) -> UUID:
    org_member = await org_member_service.get_team_member(user_id)

    if org_member:
        return org_member.id

    raise RuntimeError(f"No organization member found for user {user_id}")


OrgMemberIdDep = Annotated[UUID, Depends(get_org_member_id)]


async def get_user_env(
    user_org_id: UserOrgIdDep,
    org_service: OrganizationServiceDep,
) -> OrgEnvironment:
    env = await org_service.get_env(org_id=user_org_id, env_type=EnvironmentType.PROD)

    if env is None:
        raise RuntimeError(
            f"No production environment found for organization {user_org_id}"
        )

    return env


UserEnvDep = Annotated[OrgEnvironment, Depends(get_user_env)]


def get_salesforce_connection_service(
    db_session: AsyncDbSessionDep,
) -> SalesforceConnectionService:
    integration_user_repo = IntegrationUserRepository(db_session)
    integration_cfg_repo = IntegrationConfigRepository(db_session)

    return SalesforceConnectionService(
        db_session=db_session,
        integration_user_repo=integration_user_repo,
        integration_cfg_repo=integration_cfg_repo,
        auth_url=str(config.salesforce.auth_url),
        token_url=str(config.salesforce.token_url),
        redirect_uri=str(config.salesforce.redirect_uri),
        flow_type=OAuthFlowType.PKCE,
    )


SalesforceConnectionServiceDep = Annotated[
    SalesforceConnectionService, Depends(get_salesforce_connection_service)
]


def get_google_connection_service(
    db_session: AsyncDbSessionDep,
) -> GoogleConnectionService:
    integration_user_repo = IntegrationUserRepository(db_session)
    integration_cfg_repo = IntegrationConfigRepository(db_session)

    # Create without integration_source - will be set by router
    return GoogleConnectionService(
        db_session=db_session,
        integration_user_repo=integration_user_repo,
        integration_cfg_repo=integration_cfg_repo,
        auth_url=str(config.google.auth_url),
        token_url=str(config.google.token_url),
        redirect_uri=str(config.google.redirect_uri),
        flow_type=OAuthFlowType.STANDARD,
    )


GoogleConnectionServiceDep = Annotated[
    GoogleConnectionService, Depends(get_google_connection_service)
]


def get_hubspot_connection_service(
    db_session: AsyncDbSessionDep,
) -> HubSpotConnectionService:
    integration_user_repo = IntegrationUserRepository(db_session)
    integration_cfg_repo = IntegrationConfigRepository(db_session)

    return HubSpotConnectionService(
        db_session=db_session,
        integration_user_repo=integration_user_repo,
        integration_cfg_repo=integration_cfg_repo,
        auth_url=str(config.hubspot.auth_url),
        token_url=str(config.hubspot.token_url),
        redirect_uri=str(config.hubspot.redirect_uri),
        flow_type=OAuthFlowType.PKCE,
    )


HubSpotConnectionServiceDep = Annotated[
    HubSpotConnectionService, Depends(get_hubspot_connection_service)
]


def get_user_integrations(
    user_id: AuthenticatedUserIdDep,
    user_env: UserEnvDep,
    integration_cfg_service: IntegrationConfigServiceDep,
    salesforce_connection_service: SalesforceConnectionServiceDep,
    hubspot_connection_service: HubSpotConnectionServiceDep,
    google_connection_service: GoogleConnectionServiceDep,
    db_session: AsyncDbSessionDep,
) -> UserIntegrations:
    return UserIntegrations(
        user_id=user_id,
        environment=user_env,
        integration_cfg_service=integration_cfg_service,
        salesforce_connection_service=salesforce_connection_service,
        hubspot_connection_service=hubspot_connection_service,
        google_connection_service=google_connection_service,
        db_session=db_session,
    )


UserIntegrationsDep = Annotated[UserIntegrations, Depends(get_user_integrations)]


def get_crm_service(
    user_id: AuthenticatedUserIdDep,
    user_integrations: UserIntegrationsDep,
    db_session: AsyncDbSessionDep,
) -> CRMService:
    field_mapping_repo = SalesforceFieldMappingRepository(db_session)
    return CRMService(
        user_id=user_id,
        user_integrations=user_integrations,
        field_mapping_repo=field_mapping_repo,
    )


CRMServiceDep = Annotated[CRMService, Depends(get_crm_service)]


def get_calendar_service(
    user_integrations: UserIntegrationsDep,
) -> CalendarService:
    return CalendarService(user_integrations=user_integrations)


CalendarServiceDep = Annotated[CalendarService, Depends(get_calendar_service)]


def get_file_service(
    user_integrations: UserIntegrationsDep,
) -> FileService:
    return FileService(user_integrations=user_integrations)


FileServiceDep = Annotated[FileService, Depends(get_file_service)]
