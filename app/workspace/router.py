from fastapi import APIRouter, Depends

from app.auth.dependencies import get_authenticated_user_id
from app.workspace.routers.admin import router as admin_router
from app.workspace.routers.calendar import router as calendar_router
from app.workspace.routers.crm import router as crm_router
from app.workspace.routers.file import router as file_router
from app.workspace.routers.google_calendar import (
    router as google_calendar_router,
)
from app.workspace.routers.google_gmail import router as google_gmail_router
from app.workspace.routers.hubspot import router as hubspot_router
from app.workspace.routers.integrations import router as integrations_router
from app.workspace.routers.organization import router as organization_router
from app.workspace.routers.salesforce import router as salesforce_router

router = APIRouter(dependencies=[Depends(get_authenticated_user_id)])


router.include_router(admin_router)
router.include_router(crm_router)
router.include_router(google_gmail_router)
router.include_router(google_calendar_router)
router.include_router(hubspot_router)
router.include_router(integrations_router)
router.include_router(salesforce_router)
router.include_router(organization_router)
router.include_router(calendar_router)
router.include_router(file_router)
