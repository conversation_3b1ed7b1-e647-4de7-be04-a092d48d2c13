from app.common.exceptions.base import (
    BadRequestException,
    ForbiddenException,
    NotFoundException,
)


class IntegrationConfigError(BadRequestException):
    detail = "Integration configuration error."


class IntegrationCredentialsError(BadRequestException):
    detail = "Missing or invalid integration credentials."


class IntegrationTokenNotFoundError(NotFoundException):
    detail = "Integration token not found."


class NoAdminRightsError(ForbiddenException):
    detail = "User does not have admin rights in the organization."


class FileAlreadyExistsError(BadRequestException):
    detail = "A file with this name already exists."


class FileNameError(BadRequestException):
    detail = "File name is not valid. Remove any special characters or spaces."


class IntegrationTokenExpiredError(BadRequestException):
    detail = "Integration token is expired and cannot be refreshed."


class IntegrationTokenRefreshError(BadRequestException):
    detail = "Failed to refresh integration token."


class IntegrationDisconnectedError(BadRequestException):
    detail = "Integration is disconnected and requires re-authentication."
