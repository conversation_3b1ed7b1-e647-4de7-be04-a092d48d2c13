import uuid
from datetime import datetime

import pytest

from app.integrations.types import IntegrationSource
from app.workspace.integrations.credentials_resolver import (
    OrganizationCredentialsResolver,
    SimpleCredentialsResolver,
    UserCredentialsResolver,
    WorkspaceCredentials,
    WorkspaceSalesforceCredentials,
)
from app.workspace.models import IntegrationUser
from app.workspace.schemas import OrgEnvironment
from app.workspace.services.hubspot_connection import HubSpotConnectionService
from app.workspace.services.salesforce_connection import SalesforceConnectionService
from app.workspace.types import EnvironmentType


@pytest.fixture
def mock_environment():
    return OrgEnvironment(
        id=uuid.uuid4(),
        organization_id=uuid.uuid4(),
        type=EnvironmentType.PROD,
        created_at=datetime.now(),
        updated_at=datetime.now(),
    )


def create_mock_integration_user() -> IntegrationUser:
    integration_user = IntegrationUser()
    integration_user.id = uuid.uuid4()
    integration_user.access_token = "mock_access_token"
    integration_user.instance_url = "https://test.salesforce.com"
    integration_user.user_id = uuid.uuid4()
    return integration_user


def create_mock_integration_config() -> object:
    mock_config = type("MockIntegrationConfig", (), {})()
    mock_config.id = uuid.uuid4()
    mock_config.credentials = {"api_key": "test_key", "api_secret": "test_secret"}
    return mock_config


@pytest.mark.anyio
async def test_workspace_credentials_secrets():
    secrets = {"api_key": "test_key", "api_secret": "test_secret"}
    credentials = WorkspaceCredentials(secrets)

    assert credentials.secrets == secrets
    refreshed = await credentials.refresh_token()
    assert refreshed is credentials


@pytest.mark.anyio
async def test_workspace_salesforce_credentials_secrets(mocker, mock_environment):
    integration_user = create_mock_integration_user()
    connection_service = mocker.MagicMock(spec=SalesforceConnectionService)

    credentials = WorkspaceSalesforceCredentials(
        integration_user=integration_user,
        connection_service=connection_service,
        environment=mock_environment,
    )

    assert credentials.secrets == {
        "access_token": "mock_access_token",
        "instance_url": "https://test.salesforce.com",
    }


@pytest.mark.anyio
async def test_workspace_salesforce_credentials_refresh_success(
    mocker, mock_environment
):
    integration_user = create_mock_integration_user()
    connection_service = mocker.AsyncMock(spec=SalesforceConnectionService)

    response = mocker.MagicMock()
    response.access_token = "refreshed_access_token"
    response.instance_url = "https://refreshed.salesforce.com"

    connection_service.refresh_access_token.return_value = response

    # Mock the get_integration_user_repository method that the base class expects
    integration_user_repo = mocker.AsyncMock()

    # Create updated integration user with refreshed tokens
    updated_integration_user = create_mock_integration_user()
    updated_integration_user.access_token = "refreshed_access_token"
    updated_integration_user.instance_url = "https://refreshed.salesforce.com"

    integration_user_repo.get_by_id.return_value = updated_integration_user
    integration_user_repo.update.return_value = updated_integration_user
    connection_service.integration_user_repo = integration_user_repo

    credentials = WorkspaceSalesforceCredentials(
        integration_user=integration_user,
        connection_service=connection_service,
        environment=mock_environment,
    )

    refreshed = await credentials.refresh_token()

    assert refreshed is credentials
    assert refreshed.secrets == {
        "access_token": "refreshed_access_token",
        "instance_url": "https://refreshed.salesforce.com",
    }


@pytest.mark.anyio
async def test_workspace_salesforce_credentials_refresh_failure(
    mocker, mock_environment
):
    integration_user = create_mock_integration_user()
    connection_service = mocker.AsyncMock(spec=SalesforceConnectionService)

    connection_service.refresh_access_token.side_effect = Exception("Failed to refresh")

    # Mock the get_integration_user_repository method that the base class expects
    integration_user_repo = mocker.AsyncMock()
    integration_user_repo.get_by_id.return_value = integration_user
    integration_user_repo.update.return_value = integration_user
    connection_service.integration_user_repo = integration_user_repo

    credentials = WorkspaceSalesforceCredentials(
        integration_user=integration_user,
        connection_service=connection_service,
        environment=mock_environment,
    )

    # The new implementation raises the exception instead of swallowing it
    with pytest.raises(Exception, match="Failed to refresh"):
        await credentials.refresh_token()


@pytest.mark.anyio
async def test_simple_credentials_resolver():
    secrets = {"api_key": "test_key", "api_secret": "test_secret"}
    credentials = WorkspaceCredentials(secrets)
    resolver = SimpleCredentialsResolver(credentials)

    result = await resolver.get_credentials(source=IntegrationSource.SALESFORCE)
    assert result is credentials

    result = await resolver.get_credentials(IntegrationSource.SLACK)
    assert result is credentials


@pytest.mark.anyio
async def test_organization_resolver_get_credentials(mocker, mock_environment):
    mock_service = mocker.AsyncMock()
    mock_config = create_mock_integration_config()

    mock_service.get_integration_config.return_value = mock_config

    resolver = OrganizationCredentialsResolver(
        environment=mock_environment,
        integration_config_service=mock_service,
    )

    credentials = await resolver.get_credentials(IntegrationSource.SALESFORCE)

    assert isinstance(credentials, WorkspaceCredentials)
    assert credentials.secrets == mock_config.credentials


@pytest.mark.anyio
async def test_organization_resolver_no_config_found(mocker, mock_environment):
    mock_service = mocker.AsyncMock()

    mock_service.get_integration_config.return_value = None

    resolver = OrganizationCredentialsResolver(
        environment=mock_environment,
        integration_config_service=mock_service,
    )

    credentials = await resolver.get_credentials(IntegrationSource.SALESFORCE)

    assert isinstance(credentials, WorkspaceCredentials)
    assert credentials.secrets == {}


@pytest.mark.anyio
async def test_user_resolver_with_salesforce_credentials(mocker, mock_environment):
    user_id = uuid.uuid4()
    mock_config_service = mocker.AsyncMock()
    mock_sf_connection_service = mocker.MagicMock(spec=SalesforceConnectionService)
    mock_hubspot_connection_service = mocker.MagicMock(spec=HubSpotConnectionService)
    mock_config = create_mock_integration_config()
    mock_integration_user = create_mock_integration_user()

    mock_config_service.get_integration_config.return_value = mock_config
    mock_config_service.get_integration_user.return_value = mock_integration_user

    resolver = UserCredentialsResolver(
        environment=mock_environment,
        user_id=user_id,
        integration_config_service=mock_config_service,
        salesforce_connection_service=mock_sf_connection_service,
        hubspot_connection_service=mock_hubspot_connection_service,
        google_connection_service=mocker.MagicMock(),
    )

    credentials = await resolver.get_credentials(IntegrationSource.SALESFORCE)

    assert isinstance(credentials, WorkspaceSalesforceCredentials)
    assert credentials.secrets == {
        "access_token": "mock_access_token",
        "instance_url": "https://test.salesforce.com",
    }


@pytest.mark.anyio
async def test_user_resolver_no_integration_user(mocker, mock_environment):
    user_id = uuid.uuid4()
    mock_service = mocker.AsyncMock()
    mock_sf_connection_service = mocker.MagicMock(spec=SalesforceConnectionService)
    mock_hubspot_connection_service = mocker.MagicMock(spec=HubSpotConnectionService)
    mock_google_connection_service = mocker.MagicMock()
    mock_config = create_mock_integration_config()

    mock_service.get_integration_config.return_value = mock_config
    mock_service.get_integration_user.return_value = None

    resolver = UserCredentialsResolver(
        environment=mock_environment,
        user_id=user_id,
        integration_config_service=mock_service,
        salesforce_connection_service=mock_sf_connection_service,
        hubspot_connection_service=mock_hubspot_connection_service,
        google_connection_service=mock_google_connection_service,
    )

    credentials = await resolver.get_credentials(IntegrationSource.SALESFORCE)

    assert isinstance(credentials, WorkspaceCredentials)
    assert credentials.secrets == {}


@pytest.mark.anyio
async def test_user_resolver_non_oauth_source(mocker, mock_environment):
    user_id = uuid.uuid4()
    mock_service = mocker.AsyncMock()
    mock_sf_connection_service = mocker.MagicMock(spec=SalesforceConnectionService)
    mock_hubspot_connection_service = mocker.MagicMock(spec=HubSpotConnectionService)
    mock_google_connection_service = mocker.MagicMock()
    mock_config = create_mock_integration_config()

    mock_service.get_integration_config.return_value = mock_config

    resolver = UserCredentialsResolver(
        environment=mock_environment,
        user_id=user_id,
        integration_config_service=mock_service,
        salesforce_connection_service=mock_sf_connection_service,
        hubspot_connection_service=mock_hubspot_connection_service,
        google_connection_service=mock_google_connection_service,
    )

    credentials = await resolver.get_credentials(IntegrationSource.SLACK)

    assert isinstance(credentials, WorkspaceCredentials)
    assert credentials.secrets == mock_config.credentials
