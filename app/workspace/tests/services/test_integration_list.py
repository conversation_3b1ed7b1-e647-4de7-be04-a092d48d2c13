import uuid
from datetime import datetime

import pytest

from app.integrations.types import IntegrationSource
from app.workspace.models import IntegrationConfig, IntegrationUser
from app.workspace.schemas import OrgEnvironment
from app.workspace.schemas.integration import IntegrationInfo, IntegrationsListResponse
from app.workspace.services.integration_config import IntegrationConfigService
from app.workspace.services.integration_list import IntegrationListService
from app.workspace.types import EnvironmentType, IntegrationType


@pytest.fixture
def mock_integration_config_service(mocker):
    return mocker.AsyncMock(spec=IntegrationConfigService)


@pytest.fixture
def integration_list_service(mock_integration_config_service):
    return IntegrationListService(mock_integration_config_service)


@pytest.fixture
def mock_environment():
    return OrgEnvironment(
        id=uuid.uuid4(),
        organization_id=uuid.uuid4(),
        type=EnvironmentType.PROD,
        created_at=datetime.now(),
        updated_at=datetime.now(),
    )


@pytest.fixture
def user_id():
    return uuid.uuid4()


def create_mock_integration_config(
    mocker, source: IntegrationSource
) -> IntegrationConfig:
    config = mocker.Mock(spec=IntegrationConfig)
    config.id = uuid.uuid4()
    config.source = source
    return config


def create_mock_integration_user(mocker) -> IntegrationUser:
    return mocker.Mock(spec=IntegrationUser)


@pytest.mark.anyio
async def test_get_integrations_list_mixed_active_and_available(
    integration_list_service,
    mock_integration_config_service,
    mock_environment,
    user_id,
    mocker,
):
    salesforce_config = create_mock_integration_config(
        mocker, IntegrationSource.SALESFORCE
    )
    slack_config = create_mock_integration_config(mocker, IntegrationSource.SLACK)
    hubspot_config = create_mock_integration_config(mocker, IntegrationSource.HUBSPOT)

    configured_integrations = [salesforce_config, slack_config, hubspot_config]

    mock_integration_config_service.get_integration_configs.return_value = (
        configured_integrations
    )

    def mock_get_integration_user(config_id, _):
        if config_id == salesforce_config.id:
            return create_mock_integration_user(mocker)
        elif config_id == slack_config.id:
            return create_mock_integration_user(mocker)
        else:
            return None

    mock_integration_config_service.get_integration_user.side_effect = (
        mock_get_integration_user
    )

    result = await integration_list_service.get_integrations_list(
        user_id, mock_environment
    )

    assert isinstance(result, IntegrationsListResponse)
    assert len(result.active_integrations) == 2
    assert len(result.available_integrations) == 1

    active_sources = {integration.source for integration in result.active_integrations}
    assert IntegrationSource.SALESFORCE in active_sources
    assert IntegrationSource.SLACK in active_sources

    available_sources = {
        integration.source for integration in result.available_integrations
    }
    assert IntegrationSource.HUBSPOT in available_sources

    all_integrations = result.active_integrations + result.available_integrations
    for integration in all_integrations:
        assert isinstance(integration, IntegrationInfo)
        assert integration.source in [
            IntegrationSource.SALESFORCE,
            IntegrationSource.SLACK,
            IntegrationSource.HUBSPOT,
        ]
        assert integration.integration_type in [
            IntegrationType.CRM,
            IntegrationType.MESSAGING,
        ]
        assert integration.name is not None
        assert integration.description is not None
        assert integration.id is not None

    for integration in result.active_integrations:
        assert integration.is_active is True
    for integration in result.available_integrations:
        assert integration.is_active is False

    mock_integration_config_service.get_integration_configs.assert_called_once_with(
        mock_environment
    )
    assert mock_integration_config_service.get_integration_user.call_count == 3


@pytest.mark.anyio
async def test_get_integrations_list_all_active(
    integration_list_service,
    mock_integration_config_service,
    mock_environment,
    user_id,
    mocker,
):
    salesforce_config = create_mock_integration_config(
        mocker, IntegrationSource.SALESFORCE
    )
    slack_config = create_mock_integration_config(mocker, IntegrationSource.SLACK)

    configured_integrations = [salesforce_config, slack_config]

    mock_integration_config_service.get_integration_configs.return_value = (
        configured_integrations
    )
    mock_integration_config_service.get_integration_user.return_value = (
        create_mock_integration_user(mocker)
    )

    result = await integration_list_service.get_integrations_list(
        user_id, mock_environment
    )

    assert len(result.active_integrations) == 2
    assert len(result.available_integrations) == 0

    for integration in result.active_integrations:
        assert integration.is_active is True


@pytest.mark.anyio
async def test_get_integrations_list_none_active(
    integration_list_service,
    mock_integration_config_service,
    mock_environment,
    user_id,
    mocker,
):
    salesforce_config = create_mock_integration_config(
        mocker, IntegrationSource.SALESFORCE
    )
    slack_config = create_mock_integration_config(mocker, IntegrationSource.SLACK)

    configured_integrations = [salesforce_config, slack_config]

    mock_integration_config_service.get_integration_configs.return_value = (
        configured_integrations
    )
    mock_integration_config_service.get_integration_user.return_value = None

    result = await integration_list_service.get_integrations_list(
        user_id, mock_environment
    )

    assert len(result.active_integrations) == 0
    assert len(result.available_integrations) == 2

    for integration in result.available_integrations:
        assert integration.is_active is False


@pytest.mark.anyio
async def test_get_integrations_list_no_configured_integrations(
    integration_list_service,
    mock_integration_config_service,
    mock_environment,
    user_id,
):
    mock_integration_config_service.get_integration_configs.return_value = []

    result = await integration_list_service.get_integrations_list(
        user_id, mock_environment
    )

    assert isinstance(result, IntegrationsListResponse)
    assert len(result.active_integrations) == 0
    assert len(result.available_integrations) == 0

    mock_integration_config_service.get_integration_configs.assert_called_once_with(
        mock_environment
    )
    mock_integration_config_service.get_integration_user.assert_not_called()


@pytest.mark.anyio
async def test_get_integrations_list_unknown_integration_source(
    integration_list_service,
    mock_integration_config_service,
    mock_environment,
    user_id,
    mocker,
):
    gcs_config = create_mock_integration_config(mocker, IntegrationSource.GCS)
    salesforce_config = create_mock_integration_config(
        mocker, IntegrationSource.SALESFORCE
    )

    configured_integrations = [gcs_config, salesforce_config]

    mock_integration_config_service.get_integration_configs.return_value = (
        configured_integrations
    )
    mock_integration_config_service.get_integration_user.return_value = None

    result = await integration_list_service.get_integrations_list(
        user_id, mock_environment
    )

    assert len(result.active_integrations) == 0
    assert len(result.available_integrations) == 1
    assert result.available_integrations[0].source == IntegrationSource.SALESFORCE


@pytest.mark.anyio
async def test_get_integrations_list_calendar_integrations(
    integration_list_service,
    mock_integration_config_service,
    mock_environment,
    user_id,
    mocker,
):
    google_calendar_config = create_mock_integration_config(
        mocker, IntegrationSource.GOOGLE_CALENDAR
    )
    outlook_calendar_config = create_mock_integration_config(
        mocker, IntegrationSource.OUTLOOK_CALENDAR
    )

    configured_integrations = [google_calendar_config, outlook_calendar_config]

    mock_integration_config_service.get_integration_configs.return_value = (
        configured_integrations
    )

    def mock_get_integration_user(config_id, _):
        if config_id == google_calendar_config.id:
            return create_mock_integration_user(mocker)
        else:
            return None

    mock_integration_config_service.get_integration_user.side_effect = (
        mock_get_integration_user
    )

    result = await integration_list_service.get_integrations_list(
        user_id, mock_environment
    )

    assert len(result.active_integrations) == 1
    assert len(result.available_integrations) == 1

    assert result.active_integrations[0].source == IntegrationSource.GOOGLE_CALENDAR
    assert result.active_integrations[0].integration_type == IntegrationType.CALENDAR
    assert result.active_integrations[0].is_active is True

    assert result.available_integrations[0].source == IntegrationSource.OUTLOOK_CALENDAR
    assert result.available_integrations[0].integration_type == IntegrationType.CALENDAR
    assert result.available_integrations[0].is_active is False


@pytest.mark.anyio
async def test_get_integrations_list_metadata_content(
    integration_list_service,
    mock_integration_config_service,
    mock_environment,
    user_id,
    mocker,
):
    salesforce_config = create_mock_integration_config(
        mocker, IntegrationSource.SALESFORCE
    )

    configured_integrations = [salesforce_config]

    mock_integration_config_service.get_integration_configs.return_value = (
        configured_integrations
    )
    mock_integration_config_service.get_integration_user.return_value = None

    result = await integration_list_service.get_integrations_list(
        user_id, mock_environment
    )

    assert len(result.available_integrations) == 1
    salesforce_integration = result.available_integrations[0]

    assert salesforce_integration.id == "salesforce"
    assert salesforce_integration.source == IntegrationSource.SALESFORCE
    assert salesforce_integration.integration_type == IntegrationType.CRM
    assert salesforce_integration.name == "Salesforce"
    assert (
        salesforce_integration.description
        == "Connect your Salesforce CRM to sync accounts and opportunities"
    )
    assert salesforce_integration.is_active is False


def test_get_integration_metadata(integration_list_service):
    metadata = integration_list_service._get_integration_metadata()

    expected_sources = {
        IntegrationSource.SALESFORCE,
        IntegrationSource.HUBSPOT,
        IntegrationSource.SLACK,
        IntegrationSource.TEAMS,
        IntegrationSource.GOOGLE_CALENDAR,
        IntegrationSource.GMAIL,
        IntegrationSource.OUTLOOK_CALENDAR,
    }

    assert set(metadata.keys()) == expected_sources

    for _source, meta in metadata.items():
        assert "id" in meta
        assert "name" in meta
        assert "description" in meta
        assert isinstance(meta["id"], str)
        assert isinstance(meta["name"], str)
        assert isinstance(meta["description"], str)
        assert len(meta["id"]) > 0
        assert len(meta["name"]) > 0
        assert len(meta["description"]) > 0
