import uuid
from datetime import UTC, datetime, timedelta
from unittest.mock import AsyncMock, <PERSON><PERSON>

import pytest

from app.common.oauth.flow_manager import OAuthFlowType
from app.integrations.types import IntegrationSource
from app.workspace.exceptions import (
    IntegrationConfigError,
    IntegrationCredentialsError,
    IntegrationTokenNotFoundError,
)
from app.workspace.models import IntegrationConfig, IntegrationUser
from app.workspace.schemas import (
    GoogleOAuthCredentials,
    GoogleOAuthTokenResponse,
    OrgEnvironment,
)
from app.workspace.services.google_connection import GoogleConnectionService
from app.workspace.types import EnvironmentType


@pytest.fixture
def mock_environment():
    return OrgEnvironment(
        id=uuid.uuid4(),
        organization_id=uuid.uuid4(),
        type=EnvironmentType.PROD,
        created_at=datetime.now(UTC),
        updated_at=datetime.now(UTC),
    )


@pytest.fixture
def service_mocks(mocker):
    db_session_mock = mocker.Mock()
    db_session_mock.commit = mocker.AsyncMock()
    db_session_mock.refresh = mocker.AsyncMock()

    return {
        "db_session": db_session_mock,
        "integration_user_repo": mocker.Mock(),
        "integration_cfg_repo": mocker.Mock(),
        "oauth_flow_manager": mocker.Mock(),
    }


@pytest.fixture
def google_connection_service(service_mocks):
    service = GoogleConnectionService(
        db_session=service_mocks["db_session"],
        integration_user_repo=service_mocks["integration_user_repo"],
        integration_cfg_repo=service_mocks["integration_cfg_repo"],
        auth_url="https://accounts.google.com/o/oauth2/auth",
        token_url="https://oauth2.googleapis.com/token",  # noqa: S106
        redirect_uri="https://app.example.com/oauth/callback",
        flow_type=OAuthFlowType.STANDARD,
    )
    service.oauth_flow_manager = service_mocks["oauth_flow_manager"]
    return service


@pytest.fixture
def test_data(mock_environment):
    user_id = uuid.uuid4()
    integration_config_id = uuid.uuid4()
    integration_user_id = uuid.uuid4()

    integration_config = IntegrationConfig()
    integration_config.id = integration_config_id
    integration_config.organization_id = mock_environment.organization_id
    integration_config.source = IntegrationSource.GOOGLE_CALENDAR
    integration_config.credentials = {
        "client_id": "google_client_id",
        "client_secret": "google_client_secret",
    }

    integration_user = IntegrationUser()
    integration_user.id = integration_user_id
    integration_user.user_id = user_id
    integration_user.integration_config_id = integration_config_id
    integration_user.external_user_id = "google_user_123"
    integration_user.access_token = "google_access_token"
    integration_user.refresh_token = "google_refresh_token"
    integration_user.scope = "https://www.googleapis.com/auth/calendar"
    integration_user.token_type = "Bearer"
    integration_user.expires_at = datetime.now(UTC) + timedelta(hours=1)
    integration_user.last_refreshed_at = datetime.now(UTC)

    return {
        "user_id": user_id,
        "environment": mock_environment,
        "integration_config_id": integration_config_id,
        "integration_user_id": integration_user_id,
        "integration_config": integration_config,
        "integration_user": integration_user,
    }


class TestGoogleConnectionService:
    def test_integration_source_property(self, google_connection_service):
        # The GoogleConnectionService is dynamic, so it returns None
        assert google_connection_service.integration_source is None

    def test_default_token_expiry_seconds_property(self, google_connection_service):
        assert google_connection_service.default_token_expiry_seconds == 3600

    def test_get_redirect_uri_for_token_exchange(self, google_connection_service):
        assert (
            google_connection_service._get_redirect_uri_for_token_exchange()
            == "postmessage"
        )

    @pytest.mark.anyio
    async def test_get_config_and_credentials_success(
        self, mocker, google_connection_service, service_mocks, test_data
    ):
        service_mocks["integration_cfg_repo"].get_by_org_and_source = mocker.AsyncMock(
            return_value=test_data["integration_config"]
        )

        (
            config,
            credentials,
        ) = await google_connection_service._get_config_and_credentials(
            test_data["environment"], IntegrationSource.GOOGLE_CALENDAR
        )

        assert config == test_data["integration_config"]
        assert isinstance(credentials, GoogleOAuthCredentials)
        assert credentials.client_id == "google_client_id"
        assert credentials.client_secret == "google_client_secret"

        service_mocks[
            "integration_cfg_repo"
        ].get_by_org_and_source.assert_called_once_with(
            org_id=test_data["environment"].organization_id,
            source=IntegrationSource.GOOGLE_CALENDAR,
        )

    @pytest.mark.anyio
    async def test_get_config_and_credentials_no_config(
        self, mocker, google_connection_service, service_mocks, test_data
    ):
        service_mocks["integration_cfg_repo"].get_by_org_and_source = mocker.AsyncMock(
            return_value=None
        )

        with pytest.raises(
            IntegrationConfigError, match="No google_calendar integration config found"
        ):
            await google_connection_service._get_config_and_credentials(
                test_data["environment"], IntegrationSource.GOOGLE_CALENDAR
            )

    def test_validate_credentials_success(self, google_connection_service):
        credentials = GoogleOAuthCredentials(
            client_id="test_client_id",
            client_secret="test_client_secret",  # noqa: S106
        )

        google_connection_service._validate_credentials(credentials)

    def test_validate_credentials_missing_client_id(self, google_connection_service):
        credentials = GoogleOAuthCredentials(
            client_id="",
            client_secret="test_client_secret",  # noqa: S106
        )

        with pytest.raises(
            IntegrationCredentialsError,
            match="Missing Google client_id and client_secret",
        ):
            google_connection_service._validate_credentials(credentials)

    def test_validate_credentials_missing_client_secret(
        self, google_connection_service
    ):
        credentials = GoogleOAuthCredentials(
            client_id="test_client_id", client_secret=""
        )

        with pytest.raises(
            IntegrationCredentialsError,
            match="Missing Google client_id and client_secret",
        ):
            google_connection_service._validate_credentials(credentials)

    @pytest.mark.anyio
    async def test_extract_user_info_from_token_success(
        self, mocker, google_connection_service
    ):
        mock_client = Mock()
        mock_client.get_user_info = AsyncMock(
            return_value={"id": "primary", "summary": "Test Calendar"}
        )

        mocker.patch(
            "app.workspace.services.google_connection.GoogleConnectionService._create_google_client",
            return_value=mock_client,
        )
        credentials = GoogleOAuthCredentials(
            client_id="test_client_id",
            client_secret="test_client_secret",  # noqa: S106
        )
        token_data = {
            "access_token": "test_access_token",
            "refresh_token": "test_refresh_token",
        }

        result = await google_connection_service._extract_user_info_from_token(
            token_data, credentials, IntegrationSource.GOOGLE_CALENDAR
        )

        assert result == {"external_user_id": "primary"}
        mock_client.get_user_info.assert_called_once()

    @pytest.mark.anyio
    async def test_extract_user_info_from_token_no_id(
        self, mocker, google_connection_service
    ):
        mock_client = Mock()
        mock_client.get_user_info = AsyncMock(return_value={"summary": "Test Calendar"})

        mocker.patch(
            "app.workspace.services.google_connection.GoogleConnectionService._create_google_client",
            return_value=mock_client,
        )
        credentials = GoogleOAuthCredentials(
            client_id="test_client_id",
            client_secret="test_client_secret",  # noqa: S106
        )
        token_data = {
            "access_token": "test_access_token",
            "refresh_token": "test_refresh_token",
        }

        result = await google_connection_service._extract_user_info_from_token(
            token_data, credentials, IntegrationSource.GOOGLE_CALENDAR
        )

        assert result == {"external_user_id": "unknown"}

    def test_create_token_response(self, google_connection_service, test_data):
        expires_at = datetime.now(UTC) + timedelta(hours=1)

        result = google_connection_service._create_token_response(
            test_data["integration_user"], expires_at
        )

        assert isinstance(result, GoogleOAuthTokenResponse)
        assert result.external_user_id == "google_user_123"
        assert result.access_token == "google_access_token"
        assert result.expires_at == expires_at

    @pytest.mark.anyio
    async def test_generate_oauth_authorization_uri_success(
        self, mocker, google_connection_service, service_mocks, test_data
    ):
        service_mocks["integration_cfg_repo"].get_by_org_and_source = mocker.AsyncMock(
            return_value=test_data["integration_config"]
        )
        service_mocks[
            "oauth_flow_manager"
        ].generate_authorization_uri.return_value = (
            "https://accounts.google.com/oauth/auth"
        )

        result = await google_connection_service.generate_oauth_authorization_uri(
            user_id=test_data["user_id"],
            environment=test_data["environment"],
            dynamic_integration_source=IntegrationSource.GOOGLE_CALENDAR,
        )

        assert result == "https://accounts.google.com/oauth/auth"
        service_mocks[
            "integration_cfg_repo"
        ].get_by_org_and_source.assert_called_once_with(
            org_id=test_data["environment"].organization_id,
            source=IntegrationSource.GOOGLE_CALENDAR,
        )
        service_mocks[
            "oauth_flow_manager"
        ].generate_authorization_uri.assert_called_once()

    @pytest.mark.anyio
    async def test_generate_oauth_authorization_uri_with_custom_scope(
        self, mocker, google_connection_service, service_mocks, test_data
    ):
        service_mocks["integration_cfg_repo"].get_by_org_and_source = mocker.AsyncMock(
            return_value=test_data["integration_config"]
        )
        service_mocks[
            "oauth_flow_manager"
        ].generate_authorization_uri.return_value = (
            "https://accounts.google.com/oauth/auth"
        )

        custom_scope = "https://www.googleapis.com/auth/calendar.readonly"
        await google_connection_service.generate_oauth_authorization_uri(
            user_id=test_data["user_id"],
            environment=test_data["environment"],
            dynamic_integration_source=IntegrationSource.GOOGLE_CALENDAR,
            scope=custom_scope,
        )

        call_args = service_mocks[
            "oauth_flow_manager"
        ].generate_authorization_uri.call_args
        assert call_args.kwargs["scope"] == custom_scope

    @pytest.mark.anyio
    async def test_generate_oauth_authorization_uri_missing_credentials(
        self, mocker, google_connection_service, service_mocks, test_data
    ):
        config = test_data["integration_config"]
        config.credentials = {"client_id": "", "client_secret": ""}
        service_mocks["integration_cfg_repo"].get_by_org_and_source = mocker.AsyncMock(
            return_value=config
        )

        with pytest.raises(IntegrationCredentialsError):
            await google_connection_service.generate_oauth_authorization_uri(
                user_id=test_data["user_id"],
                environment=test_data["environment"],
                dynamic_integration_source=IntegrationSource.GOOGLE_CALENDAR,
            )

    @pytest.mark.anyio
    async def test_generate_oauth_authorization_uri_no_config(
        self, mocker, google_connection_service, service_mocks, test_data
    ):
        service_mocks["integration_cfg_repo"].get_by_org_and_source = mocker.AsyncMock(
            return_value=None
        )

        with pytest.raises(IntegrationConfigError):
            await google_connection_service.generate_oauth_authorization_uri(
                user_id=test_data["user_id"],
                environment=test_data["environment"],
                dynamic_integration_source=IntegrationSource.GOOGLE_CALENDAR,
            )

    @pytest.mark.anyio
    async def test_process_oauth_callback_new_user(
        self, mocker, google_connection_service, service_mocks, test_data
    ):
        service_mocks["integration_cfg_repo"].get_by_org_and_source = mocker.AsyncMock(
            return_value=test_data["integration_config"]
        )

        mock_client = Mock()
        mock_client.get_user_info = AsyncMock(
            return_value={"id": "google_user_123", "summary": "Test Calendar"}
        )

        mocker.patch(
            "app.workspace.services.google_connection.GoogleConnectionService._create_google_client",
            return_value=mock_client,
        )
        token_data = {
            "access_token": "new_access_token",
            "refresh_token": "new_refresh_token",
            "expires_in": 3600,
            "scope": "https://www.googleapis.com/auth/calendar",
            "token_type": "Bearer",
        }
        service_mocks["oauth_flow_manager"].exchange_code_for_token = mocker.AsyncMock(
            return_value=token_data
        )

        service_mocks[
            "integration_user_repo"
        ].get_by_user_and_integration = mocker.AsyncMock(return_value=None)

        created_integration_user = IntegrationUser()
        created_integration_user.external_user_id = "google_user_123"
        created_integration_user.access_token = "new_access_token"
        created_integration_user.refresh_token = "new_refresh_token"
        created_integration_user.scope = "https://www.googleapis.com/auth/calendar"
        created_integration_user.token_type = "Bearer"
        created_integration_user.expires_at = datetime.now(UTC) + timedelta(hours=1)

        service_mocks["integration_user_repo"].create = mocker.AsyncMock(
            return_value=created_integration_user
        )

        result = await google_connection_service.process_oauth_callback(
            user_id=test_data["user_id"],
            environment=test_data["environment"],
            code="auth_code",
            state="state_value",
            dynamic_integration_source=IntegrationSource.GOOGLE_CALENDAR,
        )

        assert isinstance(result, GoogleOAuthTokenResponse)
        assert result.external_user_id == "google_user_123"
        assert result.access_token == "new_access_token"

        service_mocks["integration_user_repo"].create.assert_called_once()
        service_mocks["db_session"].commit.assert_called_once()
        service_mocks["db_session"].refresh.assert_called_once_with(
            created_integration_user
        )

    @pytest.mark.anyio
    async def test_process_oauth_callback_existing_user(
        self, mocker, google_connection_service, service_mocks, test_data
    ):
        service_mocks["integration_cfg_repo"].get_by_org_and_source = mocker.AsyncMock(
            return_value=test_data["integration_config"]
        )

        mock_client = Mock()
        mock_client.get_user_info = AsyncMock(
            return_value={"id": "google_user_123", "summary": "Test Calendar"}
        )

        mocker.patch(
            "app.workspace.services.google_connection.GoogleConnectionService._create_google_client",
            return_value=mock_client,
        )
        token_data = {
            "access_token": "updated_access_token",
            "refresh_token": "updated_refresh_token",
            "expires_in": 3600,
        }
        service_mocks["oauth_flow_manager"].exchange_code_for_token = mocker.AsyncMock(
            return_value=token_data
        )

        service_mocks[
            "integration_user_repo"
        ].get_by_user_and_integration = mocker.AsyncMock(
            return_value=test_data["integration_user"]
        )

        updated_integration_user = IntegrationUser()
        updated_integration_user.external_user_id = "google_user_123"
        updated_integration_user.access_token = "updated_access_token"
        updated_integration_user.refresh_token = "updated_refresh_token"
        updated_integration_user.expires_at = datetime.now(UTC) + timedelta(hours=1)

        service_mocks["integration_user_repo"].update = mocker.AsyncMock(
            return_value=updated_integration_user
        )

        result = await google_connection_service.process_oauth_callback(
            user_id=test_data["user_id"],
            environment=test_data["environment"],
            code="auth_code",
            state="state_value",
            dynamic_integration_source=IntegrationSource.GOOGLE_CALENDAR,
        )

        assert isinstance(result, GoogleOAuthTokenResponse)
        assert result.external_user_id == "google_user_123"
        assert result.access_token == "updated_access_token"

        service_mocks["integration_user_repo"].update.assert_called_once()
        service_mocks["db_session"].commit.assert_called_once()
        service_mocks["db_session"].refresh.assert_called_once_with(
            updated_integration_user
        )

    @pytest.mark.anyio
    async def test_refresh_access_token_success(
        self, mocker, google_connection_service, service_mocks, test_data
    ):
        service_mocks["integration_user_repo"].get_by_id = mocker.AsyncMock(
            return_value=test_data["integration_user"]
        )
        service_mocks["integration_cfg_repo"].get_by_org_and_source = mocker.AsyncMock(
            return_value=test_data["integration_config"]
        )

        token_data = {
            "access_token": "refreshed_access_token",
            "expires_in": 3600,
            "scope": "https://www.googleapis.com/auth/calendar",
            "token_type": "Bearer",
        }
        service_mocks["oauth_flow_manager"].refresh_access_token = mocker.AsyncMock(
            return_value=token_data
        )

        refreshed_integration_user = IntegrationUser()
        refreshed_integration_user.external_user_id = "google_user_123"
        refreshed_integration_user.access_token = "refreshed_access_token"
        refreshed_integration_user.refresh_token = "google_refresh_token"
        refreshed_integration_user.expires_at = datetime.now(UTC) + timedelta(hours=1)

        service_mocks["integration_user_repo"].update = mocker.AsyncMock(
            return_value=refreshed_integration_user
        )

        result = await google_connection_service.refresh_access_token(
            integration_user_id=test_data["integration_user_id"],
            environment=test_data["environment"],
            dynamic_integration_source=IntegrationSource.GOOGLE_CALENDAR,
        )

        assert isinstance(result, GoogleOAuthTokenResponse)
        assert result.external_user_id == "google_user_123"
        assert result.access_token == "refreshed_access_token"

        service_mocks["integration_user_repo"].get_by_id.assert_called_once_with(
            test_data["integration_user_id"]
        )
        service_mocks["oauth_flow_manager"].refresh_access_token.assert_called_once()
        service_mocks["integration_user_repo"].update.assert_called_once()
        service_mocks["db_session"].commit.assert_called_once()
        service_mocks["db_session"].refresh.assert_called_once_with(
            refreshed_integration_user
        )

    @pytest.mark.anyio
    async def test_refresh_access_token_user_not_found(
        self, mocker, google_connection_service, service_mocks, test_data
    ):
        service_mocks["integration_user_repo"].get_by_id = mocker.AsyncMock(
            return_value=None
        )

        with pytest.raises(IntegrationTokenNotFoundError):
            await google_connection_service.refresh_access_token(
                integration_user_id=test_data["integration_user_id"],
                environment=test_data["environment"],
                dynamic_integration_source=IntegrationSource.GOOGLE_CALENDAR,
            )

    @pytest.mark.anyio
    async def test_remove_connection_success(
        self, mocker, google_connection_service, service_mocks, test_data
    ):
        service_mocks["integration_cfg_repo"].get_by_org_and_source = mocker.AsyncMock(
            return_value=test_data["integration_config"]
        )

        service_mocks[
            "integration_user_repo"
        ].get_by_user_and_integration = mocker.AsyncMock(
            return_value=test_data["integration_user"]
        )
        service_mocks["integration_user_repo"].delete = mocker.AsyncMock()

        await google_connection_service.remove_connection(
            user_id=test_data["user_id"],
            environment=test_data["environment"],
            dynamic_integration_source=IntegrationSource.GOOGLE_CALENDAR,
        )

        service_mocks[
            "integration_user_repo"
        ].get_by_user_and_integration.assert_called_once_with(
            user_id=test_data["user_id"],
            integration_config_id=test_data["integration_config"].id,
        )
        service_mocks["integration_user_repo"].delete.assert_called_once_with(
            test_data["integration_user"].id
        )
        service_mocks["db_session"].commit.assert_called_once()

    @pytest.mark.anyio
    async def test_remove_connection_not_found(
        self, mocker, google_connection_service, service_mocks, test_data
    ):
        service_mocks["integration_cfg_repo"].get_by_org_and_source = mocker.AsyncMock(
            return_value=test_data["integration_config"]
        )

        service_mocks[
            "integration_user_repo"
        ].get_by_user_and_integration = mocker.AsyncMock(return_value=None)

        with pytest.raises(IntegrationTokenNotFoundError):
            await google_connection_service.remove_connection(
                user_id=test_data["user_id"],
                environment=test_data["environment"],
                dynamic_integration_source=IntegrationSource.GOOGLE_CALENDAR,
            )
