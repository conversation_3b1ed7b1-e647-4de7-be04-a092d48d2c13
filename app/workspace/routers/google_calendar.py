from fastapi import APIRouter, Query

from app.auth.dependencies import AuthenticatedUserIdDep
from app.common.helpers.logger import get_logger
from app.integrations.types import IntegrationSource
from app.workspace.dependencies import (
    GoogleConnectionServiceDep,
    UserEnvDep,
)

logger = get_logger()

router = APIRouter()


@router.get("/google/calendar/auth-url")
async def get_google_calendar_auth_url(
    user_id: AuthenticatedUserIdDep,
    user_env: UserEnvDep,
    service: GoogleConnectionServiceDep,
):
    oauth_uri = await service.generate_oauth_authorization_uri(
        user_id=user_id,
        environment=user_env,
        dynamic_integration_source=IntegrationSource.GOOGLE_CALENDAR,
    )

    return {"auth_url": oauth_uri}


@router.get("/google/calendar/callback")
async def process_google_calendar_callback(
    user_id: AuthenticatedUserIdDep,
    user_env: UserEnvDep,
    service: GoogleConnectionServiceDep,
    code: str = Query(...),
    state: str = Query(...),
):
    calendar_token = await service.process_oauth_callback(
        user_id=user_id,
        environment=user_env,
        code=code,
        state=state,
        dynamic_integration_source=IntegrationSource.GOOGLE_CALENDAR,
    )

    return {
        "message": "OAuth succeed",
        "calendar_user_id": calendar_token.external_user_id,
        "user_id": str(user_id),
    }


@router.delete("/google/calendar/connection")
async def remove_google_calendar_connection(
    user_id: AuthenticatedUserIdDep,
    user_env: UserEnvDep,
    service: GoogleConnectionServiceDep,
):
    await service.remove_connection(
        user_id=user_id,
        environment=user_env,
        dynamic_integration_source=IntegrationSource.GOOGLE_CALENDAR,
    )

    return {"message": "Google Calendar connection removed successfully"}
