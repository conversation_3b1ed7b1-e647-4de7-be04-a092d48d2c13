from fastapi import (
    APIRouter,
    BackgroundTasks,
    Depends,
    File,
    HTTPException,
    Path,
    UploadFile,
)

from app.workspace.dependencies import (
    FileServiceDep,
    OrganizationTeamServiceDep,
    UserOrgIdDep,
    require_admin,
)
from app.workspace.exceptions import FileAlreadyExistsError, FileNameError
from app.workspace.schemas import (
    OrganizationMemberProfileList,
    UploadTeamDocumentResponse,
)

"""
Router for admin-only endpoints
"""
router = APIRouter(prefix="/admin", dependencies=[Depends(require_admin)])


@router.get(
    "/member_profiles",
    response_model=OrganizationMemberProfileList,
    name="get_member_profiles",
)
async def get_member_profiles(
    org_id: UserOrgIdDep, service: OrganizationTeamServiceDep
):
    members = await service.get_team_member_profiles(org_id)
    return OrganizationMemberProfileList(members=members)


@router.post("/team-documents", response_model=UploadTeamDocumentResponse)
async def upload_team_document(
    file_service: FileServiceDep,
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...),
) -> UploadTeamDocumentResponse:
    try:
        response = await file_service.upload_team_document(
            file_obj=file.file,
            file_name=file.filename,
            content_type=file.content_type,
        )
    except FileAlreadyExistsError as e:
        raise HTTPException(status_code=409, detail=str(e))
    except FileNameError as e:
        raise HTTPException(status_code=400, detail=str(e))

    # Trigger background file processing
    background_tasks.add_task(
        file_service.trigger_file_processing,
        f"pearl-org-{file_service.user_integrations.org_id}",
    )

    return response


@router.delete("/team-documents/{document_id}")
async def delete_team_document(
    file_service: FileServiceDep,
    document_id: str = Path(..., description="The ID of the document to delete"),
) -> dict[str, str]:
    await file_service.delete_team_document(document_id)
    return {"message": "Document deleted successfully"}
