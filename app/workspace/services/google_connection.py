from datetime import datetime

from sqlalchemy.ext.asyncio import AsyncSession

from app.common.helpers.logger import get_logger
from app.common.oauth.flow_manager import OAuthFlowType
from app.integrations.adapters.gmail.client import Gmail<PERSON>lient
from app.integrations.adapters.google_calendar.client import GoogleCalendarClient
from app.integrations.services.oauth_scope_helper import OAuthScopeHelper
from app.integrations.types import IntegrationSource
from app.workspace.exceptions import (
    IntegrationConfigError,
    IntegrationCredentialsError,
)
from app.workspace.models import IntegrationConfig
from app.workspace.models.integration_user import IntegrationUser
from app.workspace.repositories.integration_config import IntegrationConfigRepository
from app.workspace.repositories.integration_user import IntegrationUserRepository
from app.workspace.schemas import (
    GoogleOAuthCredentials,
    GoogleOAuthTokenResponse,
    OrgEnvironment,
)
from app.workspace.services.base.oauth_connection import BaseOAuthConnection

logger = get_logger()


class GoogleConnectionService(
    BaseOAuthConnection[GoogleOAuthCredentials, GoogleOAuthTokenResponse]
):
    def __init__(
        self,
        db_session: AsyncSession,
        integration_user_repo: IntegrationUserRepository,
        integration_cfg_repo: IntegrationConfigRepository,
        auth_url: str,
        token_url: str,
        redirect_uri: str,
        flow_type: OAuthFlowType,
    ):
        super().__init__(
            db_session=db_session,
            integration_user_repo=integration_user_repo,
            integration_cfg_repo=integration_cfg_repo,
            auth_url=auth_url,
            token_url=token_url,
            redirect_uri=redirect_uri,
            flow_type=flow_type,
        )

    @property
    def integration_source(self) -> IntegrationSource | None:
        # The integration source for this service is dynamic and set by the router
        return None

    def _get_scope_from_integration_source(
        self, dynamic_integration_source: IntegrationSource | None = None
    ) -> str:
        """Get appropriate scope string from integration source."""
        if dynamic_integration_source is None:
            raise IntegrationConfigError("Integration source must be provided")
        match dynamic_integration_source:
            case IntegrationSource.GMAIL:
                return OAuthScopeHelper.get_gmail_scopes_string(full_access=False)
            case IntegrationSource.GOOGLE_CALENDAR:
                return OAuthScopeHelper.get_calendar_scopes_string()
            case _:
                raise IntegrationConfigError(
                    f"Invalid integration source: {dynamic_integration_source}"
                )

    @property
    def default_token_expiry_seconds(self) -> int:
        return 3600

    async def _get_config_and_credentials(
        self,
        environment: OrgEnvironment,
        dynamic_integration_source: IntegrationSource | None = None,
    ) -> tuple[IntegrationConfig, GoogleOAuthCredentials]:
        if dynamic_integration_source is None:
            raise IntegrationConfigError("Integration source must be provided")

        integration_config = await self.integration_cfg_repo.get_by_org_and_source(
            org_id=environment.organization_id, source=dynamic_integration_source
        )

        if not integration_config:
            raise IntegrationConfigError(
                f"No {dynamic_integration_source.value} integration config found for organization {environment.organization_id}"
            )

        return integration_config, GoogleOAuthCredentials.model_validate(
            integration_config.credentials
        )

    def _validate_credentials(self, credentials: GoogleOAuthCredentials) -> None:
        if not credentials.client_id or not credentials.client_secret:
            raise IntegrationCredentialsError(
                "Missing Google client_id and client_secret"
            )

    async def _create_google_client(
        self,
        token_data: dict,
        credentials: GoogleOAuthCredentials,
        integration_source: IntegrationSource,
    ):
        """Factory method to create appropriate Google client based on integration source."""
        scope = self._get_scope_from_integration_source(integration_source)
        client_credentials = {
            "access_token": token_data.get("access_token"),
            "refresh_token": token_data.get("refresh_token"),
            "client_id": credentials.client_id,
            "client_secret": credentials.client_secret,
            "token_uri": self.token_url,
            "scope": scope,
        }

        match integration_source:
            case IntegrationSource.GMAIL:
                return GmailClient(credentials=client_credentials)
            case IntegrationSource.GOOGLE_CALENDAR:
                return GoogleCalendarClient(credentials=client_credentials)
            case _:
                raise IntegrationConfigError(
                    f"Invalid integration source: {integration_source}"
                )

    async def _extract_user_info_from_token(
        self,
        token_data: dict,
        credentials: GoogleOAuthCredentials,
        dynamic_integration_source: IntegrationSource | None = None,
    ) -> dict[str, str]:
        if dynamic_integration_source is None:
            raise IntegrationConfigError("Integration source must be provided")

        client = await self._create_google_client(
            token_data, credentials, dynamic_integration_source
        )
        user_info = await client.get_user_info()

        match dynamic_integration_source:
            case IntegrationSource.GMAIL:
                return {
                    "external_user_id": user_info.get("emailAddress", "unknown"),
                }
            case IntegrationSource.GOOGLE_CALENDAR:
                return {
                    "external_user_id": user_info.get("id", "unknown"),
                }
            case _:
                raise IntegrationConfigError(
                    f"Invalid integration source: {dynamic_integration_source}"
                )

    def _get_redirect_uri_for_token_exchange(self) -> str:
        # Google requires "postmessage" for popup mode
        return "postmessage"

    def _create_token_response(
        self, integration_user: IntegrationUser, expires_at: datetime
    ) -> GoogleOAuthTokenResponse:
        return GoogleOAuthTokenResponse(
            external_user_id=integration_user.external_user_id,
            access_token=integration_user.access_token,
            refresh_token=integration_user.refresh_token,
            expires_at=expires_at,
        )
