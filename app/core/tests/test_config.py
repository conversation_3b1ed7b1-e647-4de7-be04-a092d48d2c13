import importlib
import types

import pytest

from app.core.config import AppConfig, DatabaseConfig
from app.core.env import Environment


def reload_config_module(_monkeypatch) -> tuple[AppConfig, types.ModuleType]:
    """
    Reloads the app.core.config module to force re-reading the APP_ENV environment variable.
    Returns a tuple of the new config instance and the reloaded module.
    """
    import app.core.config as config_mod

    importlib.reload(config_mod)
    return config_mod.config, config_mod


def test_production_config(monkeypatch):
    # Set APP_ENV to production and reload the module.
    monkeypatch.setenv("APP_ENV", Environment.PRODUCTION)
    config, config_mod = reload_config_module(monkeypatch)
    # Use the ProdConfig from the reloaded module for comparison.
    assert isinstance(config, config_mod.ProdConfig), (
        "Expected a ProdConfig instance in production environment"
    )


def test_test_config(monkeypatch):
    # Set APP_ENV to test and reload the module.
    monkeypatch.setenv("APP_ENV", Environment.TEST)
    config, config_mod = reload_config_module(monkeypatch)
    assert isinstance(config, config_mod.TestConfig), (
        "Expected a TestConfig instance in test environment"
    )
    # In TestConfig, log_level is explicitly set.
    assert config.log_level == "WARNING", (
        "Expected log_level to be 'WARNING' for TestConfig"
    )


def test_development_config(monkeypatch):
    # Set APP_ENV to a value other than production or test (e.g., development).
    monkeypatch.setenv("APP_ENV", "development")
    # Override LOG_LEVEL to test auto-setting behavior (empty string triggers auto-setting)
    monkeypatch.setenv("LOG_LEVEL", "")
    config, config_mod = reload_config_module(monkeypatch)
    assert isinstance(config, config_mod.DevConfig), (
        "Expected a DevConfig instance in development environment"
    )
    # In DevConfig, debug should be True and the auto validator should set log_level to "DEBUG".
    assert config.debug is True, "Expected debug to be True in DevConfig"
    assert config.log_level == "DEBUG", (
        "Expected log_level to be 'DEBUG' when debug is True"
    )


def test_log_level_auto_set(monkeypatch):
    # Override LOG_LEVEL environment variable to test auto-setting behavior
    monkeypatch.setenv("LOG_LEVEL", "")

    # Test that if log_level is empty, it is automatically set based on the debug flag.
    # For debug=True, log_level should become "DEBUG".
    conf_debug = AppConfig(debug=True, log_level="")
    assert conf_debug.log_level == "DEBUG", (
        "Expected log_level to be 'DEBUG' when debug is True and log_level is empty"
    )

    # For debug=False, log_level should become "INFO".
    conf_no_debug = AppConfig(debug=False, log_level="")
    assert conf_no_debug.log_level == "INFO", (
        "Expected log_level to be 'INFO' when debug is False and log_level is empty"
    )


def test_database_config_validator():
    # Test that DatabaseConfig raises a ValueError if database_url equals test_database_url.
    with pytest.raises(
        ValueError, match="database_url and test_database_url must be different"
    ):
        DatabaseConfig(
            database_url="postgresql+psycopg2://localhost/same_db",
            test_database_url="postgresql+psycopg2://localhost/same_db",
        )


def test_heroku_postgres_url_fix():
    db_config = DatabaseConfig(
        database_url="******************************/mydb",
        test_database_url="******************************/testdb",
    )

    assert str(db_config.database_url).startswith("postgresql://"), (
        "Expected postgres:// URL to be converted to postgresql://"
    )
    assert str(db_config.test_database_url).startswith("postgresql://"), (
        "Expected test postgres:// URL to be converted to postgresql://"
    )


def test_async_url_generation_local():
    db_config = DatabaseConfig(
        database_url="postgresql://user:pass@localhost:5432/mydb",
        test_database_url="postgresql://user:pass@localhost:5432/testdb",
    )

    async_url = db_config.async_database_url
    test_async_url = db_config.test_async_database_url

    assert "postgresql+asyncpg://" in async_url
    assert "postgresql+asyncpg://" in test_async_url

    assert "ssl=" not in async_url
    assert "sslmode=" not in async_url
    assert "ssl=" not in test_async_url
    assert "sslmode=" not in test_async_url


def test_async_url_generation_remote():
    db_config = DatabaseConfig(
        database_url="***************************************/mydb",
        test_database_url="***************************************/testdb",
    )

    async_url = db_config.async_database_url
    test_async_url = db_config.test_async_database_url

    assert "postgresql+asyncpg://" in async_url
    assert "postgresql+asyncpg://" in test_async_url

    assert "ssl=require" in async_url
    assert "ssl=require" in test_async_url


def test_async_url_generation_heroku():
    db_config = DatabaseConfig(
        database_url="postgres://u9ifqsm6qh2b8t:<EMAIL>:5432/d3iu3i1sg29eie",
        test_database_url="postgres://u9ifqsm6qh2b8t:<EMAIL>:5432/testdb",
    )

    async_url = db_config.async_database_url
    test_async_url = db_config.test_async_database_url

    assert async_url.startswith("postgresql+asyncpg://")
    assert test_async_url.startswith("postgresql+asyncpg://")

    assert "ssl=require" in async_url
    assert "ssl=require" in test_async_url

    assert "u9ifqsm6qh2b8t" in async_url
    assert "ec2-host.amazonaws.com" in async_url


def test_async_url_with_existing_ssl():
    db_config = DatabaseConfig(
        database_url="********************************/mydb?ssl=prefer",
        test_database_url="********************************/testdb?sslmode=disable",
    )

    async_url = db_config.async_database_url
    test_async_url = db_config.test_async_database_url

    assert async_url.count("ssl=") == 1
    assert "ssl=prefer" in async_url
    assert "ssl=require" not in async_url

    assert "sslmode=disable" in test_async_url
    assert "ssl=require" not in test_async_url


def test_async_url_with_query_params():
    db_config = DatabaseConfig(
        database_url="***************************************/mydb?param1=value1",
        test_database_url="postgresql://user:pass@localhost:5432/testdb?param2=value2",
    )

    async_url = db_config.async_database_url
    test_async_url = db_config.test_async_database_url

    assert "param1=value1" in async_url
    assert "&ssl=require" in async_url

    assert "param2=value2" in test_async_url
    assert "ssl=" not in test_async_url


def test_127_0_0_1_treated_as_local():
    db_config = DatabaseConfig(
        database_url="postgresql://user:pass@127.0.0.1:5432/mydb",
        test_database_url="postgresql://user:pass@127.0.0.1:5432/testdb",
    )

    async_url = db_config.async_database_url
    test_async_url = db_config.test_async_database_url

    assert "ssl=" not in async_url
    assert "ssl=" not in test_async_url


def test_convert_to_async_url_function():
    from app.core.config import _convert_to_async_url

    local_url = _convert_to_async_url("postgresql://user:pass@localhost:5432/db")
    assert "postgresql+asyncpg://" in local_url
    assert "ssl=" not in local_url

    local_ip_url = _convert_to_async_url("postgresql://user:pass@127.0.0.1:5432/db")
    assert "postgresql+asyncpg://" in local_ip_url
    assert "ssl=" not in local_ip_url

    remote_url = _convert_to_async_url("***************************************/db")
    assert "postgresql+asyncpg://" in remote_url
    assert "ssl=require" in remote_url

    with_params_url = _convert_to_async_url(
        "********************************/db?param=value"
    )
    assert "postgresql+asyncpg://" in with_params_url
    assert "param=value" in with_params_url
    assert "&ssl=require" in with_params_url

    with_ssl_url = _convert_to_async_url(
        "********************************/db?ssl=prefer"
    )
    assert "postgresql+asyncpg://" in with_ssl_url
    assert "ssl=prefer" in with_ssl_url
    assert "ssl=require" not in with_ssl_url
    assert with_ssl_url.count("ssl=") == 1


def test_async_url_property_immutable():
    db_config = DatabaseConfig(
        database_url="********************************/mydb",
        test_database_url="********************************/testdb",
    )

    url1 = db_config.async_database_url
    url2 = db_config.async_database_url

    assert url1 == url2

    assert url1.startswith("postgresql+asyncpg://")
    assert "ssl=require" in url1
