import json
import uuid
from collections.abc import AsyncIterator
from datetime import UTC, datetime
from uuid import uuid4

import pytest
from langchain_core.messages import HumanMessage

from app.agentic.action.schemas import ActionListItem
from app.agentic.exceptions import ThreadOwnershipError
from app.agentic.graph.graph_manager import GraphManager
from app.agentic.models import OrganizationMemberThread
from app.agentic.repository import OrganizationMemberThreadRepository
from app.agentic.schemas import (
    AccountSummaryRequest,
    ChatRequest,
    FrontendToolCall,
    FrontendToolCallResult,
    ThreadRead,
)
from app.agentic.service import AgentService
from app.workspace.schemas import OrgEnvironment
from app.workspace.types import EnvironmentType


@pytest.fixture
def mock_graph_manager(mocker):
    mock = mocker.AsyncMock(spec=GraphManager)
    return mock


@pytest.fixture(autouse=True)
def mock_create_graph_manager(mocker, mock_graph_manager):
    """Mock the create_graph_manager function to return our mock GraphManager"""
    return mocker.patch(
        "app.agentic.service.create_graph_manager", return_value=mock_graph_manager
    )


@pytest.fixture
def mock_organization_member_thread_repository(mocker):
    mock = mocker.AsyncMock(spec=OrganizationMemberThreadRepository)
    return mock


@pytest.fixture
def mock_environment():
    return OrgEnvironment(
        id=uuid4(),
        organization_id=uuid4(),
        type=EnvironmentType.PROD,
        created_at=datetime.now(),
        updated_at=datetime.now(),
    )


@pytest.fixture
def mock_async_db_session(mocker):
    mock = mocker.AsyncMock()
    return mock


@pytest.fixture
def mock_user_integrations(mocker):
    return mocker.Mock()


@pytest.fixture
def mock_langfuse_client(mocker):
    return mocker.Mock()


@pytest.fixture
def mock_langfuse_callback_handler(mocker):
    return mocker.Mock()


@pytest.fixture
def mock_graph_checkpointer(mocker):
    return mocker.Mock()


@pytest.fixture
def agent_service(
    org_id,
    user_id,
    org_member_id,
    mock_environment,
    mock_async_db_session,
    mock_user_integrations,
    mock_langfuse_client,
    mock_langfuse_callback_handler,
    mock_graph_checkpointer,
    mock_organization_member_thread_repository,
):
    return AgentService(
        org_id=org_id,
        user_id=user_id,
        org_member_id=org_member_id,
        environment=mock_environment,
        db_session=mock_async_db_session,
        user_integrations=mock_user_integrations,
        langfuse_client=mock_langfuse_client,
        langfuse_callback_handler=mock_langfuse_callback_handler,
        graph_checkpointer=mock_graph_checkpointer,
        organization_member_thread_repository=mock_organization_member_thread_repository,
    )


@pytest.fixture
def chat_request():
    return ChatRequest(
        content="Hello, how can you help me?",
        role="user",
        tool_calls=[],
        thread_id=str(uuid.uuid4()),
        crm_account_id="test-crm-account-id",
    )


@pytest.mark.anyio
async def test_process_message_stream_with_new_thread(
    agent_service,
    chat_request,
    mock_graph_manager,
    mock_organization_member_thread_repository,
    mock_async_db_session,
    org_id,
    user_id,
    org_member_id,
):
    expected_thread_id = chat_request.thread_id

    mock_organization_member_thread_repository.get_by_thread_id.return_value = None

    async def mock_stream_iterator() -> AsyncIterator[str]:
        metadata_data = {
            "thread_id": expected_thread_id,
            "crm_account_id": "test-crm-account-id",
        }
        yield f"event: metadata\ndata: {json.dumps(metadata_data)}\n\n"
        yield f"event: message\ndata: {json.dumps({'content': 'Streaming '})}\n\n"
        yield f"event: message\ndata: {json.dumps({'content': 'response...'})}\n\n"

    mock_graph_manager.stream_graph.return_value = mock_stream_iterator()

    stream = await agent_service.process_message_stream(chat_request)
    chunks = [chunk async for chunk in stream]

    assert len(chunks) == 3
    assert "event: metadata" in chunks[0]
    assert "event: message" in chunks[1]
    assert "event: message" in chunks[2]

    mock_graph_manager.stream_graph.assert_called_once()
    _, call_kwargs = mock_graph_manager.stream_graph.call_args

    assert "messages" in call_kwargs
    assert isinstance(call_kwargs["messages"][0], HumanMessage)
    assert call_kwargs["messages"][0].content == "Hello, how can you help me?"
    assert call_kwargs["crm_account_id"] == "test-crm-account-id"
    assert call_kwargs["org_id"] == org_id
    assert call_kwargs["user_id"] == user_id
    assert call_kwargs["resume"] is None

    thread_id_arg = call_kwargs["thread_id"]
    assert thread_id_arg == expected_thread_id
    assert isinstance(uuid.UUID(thread_id_arg), uuid.UUID)

    mock_organization_member_thread_repository.create.assert_called_once()
    create_args = mock_organization_member_thread_repository.create.call_args
    assert create_args[1]["thread_id"] == expected_thread_id
    assert create_args[1]["organization_member_id"] == org_member_id

    mock_async_db_session.commit.assert_called_once()


@pytest.mark.anyio
async def test_process_message_stream_with_existing_thread(
    agent_service,
    mock_graph_manager,
    mock_organization_member_thread_repository,
    mock_async_db_session,
    org_id,
    user_id,
):
    existing_thread = "789e4567-e89b-12d3-a456-************"
    chat_request = ChatRequest(
        role="user",
        content="Another question",
        tool_calls=[],
        thread_id=existing_thread,
        crm_account_id="test-crm-account-id",
    )

    mock_thread = type("MockThread", (), {"thread_id": existing_thread})()
    mock_organization_member_thread_repository.get_by_thread_id.return_value = (
        mock_thread
    )

    async def mock_stream_iterator() -> AsyncIterator[str]:
        metadata_data = {
            "thread_id": existing_thread,
            "crm_account_id": "test-crm-account-id",
        }
        yield f"event: metadata\ndata: {json.dumps(metadata_data)}\n\n"
        yield f"event: message\ndata: {json.dumps({'content': 'Streaming '})}\n\n"
        yield f"event: message\ndata: {json.dumps({'content': 'response...'})}\n\n"

    mock_graph_manager.stream_graph.return_value = mock_stream_iterator()

    stream = await agent_service.process_message_stream(chat_request)
    chunks = [chunk async for chunk in stream]

    assert len(chunks) == 3
    assert "event: metadata" in chunks[0]
    assert f'"thread_id": "{existing_thread}"' in chunks[0]
    assert "event: message" in chunks[1]
    assert '"content": "Streaming "' in chunks[1]
    assert "event: message" in chunks[2]
    assert '"content": "response..."' in chunks[2]

    mock_graph_manager.stream_graph.assert_called_once()
    _, call_kwargs = mock_graph_manager.stream_graph.call_args

    assert "messages" in call_kwargs
    assert isinstance(call_kwargs["messages"][0], HumanMessage)
    assert call_kwargs["messages"][0].content == "Another question"
    assert call_kwargs["crm_account_id"] == "test-crm-account-id"
    assert call_kwargs["org_id"] == org_id
    assert call_kwargs["user_id"] == user_id
    assert call_kwargs["resume"] is None

    thread_id_arg = call_kwargs["thread_id"]
    assert thread_id_arg == existing_thread

    mock_organization_member_thread_repository.create.assert_not_called()
    mock_async_db_session.commit.assert_not_called()


@pytest.mark.anyio
async def test_process_message_stream_without_crm_account_id(
    agent_service,
    mock_graph_manager,
):
    chat_request = ChatRequest(
        role="user",
        content="Hello, how can you help me?",
        tool_calls=[],
        thread_id=str(uuid.uuid4()),
        crm_account_id="",
    )

    await agent_service.process_message_stream(chat_request)

    mock_graph_manager.stream_graph.assert_called_once()
    _, call_kwargs = mock_graph_manager.stream_graph.call_args

    assert call_kwargs["crm_account_id"] == ""


@pytest.mark.anyio
async def test_process_message_stream_missing_thread_id(
    agent_service,
):
    mock_request = ChatRequest(
        role="user",
        content="Hello, how can you help me?",
        tool_calls=[],
        thread_id="",
        crm_account_id="test-crm-account-id",
    )

    with pytest.raises(ValueError, match="A thread_id is required"):
        await agent_service.process_message_stream(mock_request)


@pytest.mark.anyio
async def test_process_message_stream_empty_thread_id(
    agent_service,
):
    mock_request = ChatRequest(
        role="user",
        content="Hello, how can you help me?",
        tool_calls=[],
        thread_id="",
        crm_account_id="test-crm-account-id",
    )

    with pytest.raises(ValueError, match="A thread_id is required"):
        await agent_service.process_message_stream(mock_request)


@pytest.mark.anyio
async def test_process_message_stream_with_tool_call(
    agent_service,
    mock_graph_manager,
):
    existing_thread = "789e4567-e89b-12d3-a456-************"
    result = FrontendToolCallResult(action="continue")
    tool_call = FrontendToolCall(
        tool_name="update_opportunity",
        result=result,
    )
    chat_request = ChatRequest(
        role="assistant",
        content="",
        tool_calls=[tool_call],
        thread_id=existing_thread,
        crm_account_id="test-crm-account-id",
    )

    await agent_service.process_message_stream(chat_request)

    mock_graph_manager.stream_graph.assert_called_once()
    _, call_kwargs = mock_graph_manager.stream_graph.call_args

    assert call_kwargs["resume"] == result


@pytest.mark.anyio
async def test_get_threads_by_org_member_and_crm_account(
    agent_service,
    mock_organization_member_thread_repository,
    org_member_id,
):
    crm_account_id = "test-crm-account-id"
    thread1 = OrganizationMemberThread(
        id=uuid.uuid4(),
        thread_id="thread-1",
        organization_member_id=org_member_id,
        crm_account_id=crm_account_id,
        created_at=datetime.now(UTC),
        updated_at=datetime.now(UTC),
    )
    thread2 = OrganizationMemberThread(
        id=uuid.uuid4(),
        thread_id="thread-2",
        organization_member_id=org_member_id,
        crm_account_id=crm_account_id,
        created_at=datetime.now(UTC),
        updated_at=datetime.now(UTC),
    )
    threads = [thread1, thread2]

    mock_organization_member_thread_repository.get_by_org_member_and_crm_account.return_value = threads

    db_threads = await agent_service.get_threads_by_org_member_and_crm_account(
        crm_account_id
    )

    assert len(db_threads.threads) == 2
    assert db_threads.threads[0].id == "thread-1"
    assert db_threads.threads[1].id == "thread-2"
    mock_organization_member_thread_repository.get_by_org_member_and_crm_account.assert_called_once_with(
        org_member_id, crm_account_id
    )


@pytest.mark.anyio
async def test_get_thread_history_success(
    agent_service,
    mock_graph_manager,
    mock_organization_member_thread_repository,
):
    thread_id = "test-thread-id-success"
    page = 1
    size = 10

    mock_thread = ThreadRead(
        id=thread_id,
        organization_member_id=agent_service.org_member_id,
        crm_account_id="test-crm-account-id",
        created_at=datetime.now(UTC),
        updated_at=datetime.now(UTC),
        name="test-thread",
    )
    mock_organization_member_thread_repository.get_by_thread_id.return_value = (
        mock_thread
    )

    mock_response = {
        "pagination": {
            "thread_id": thread_id,
            "current_page": page,
            "page_size": size,
            "total_messages": 15,
            "total_pages": 2,
        },
        "messages": [
            {"role": "user", "content": [{"type": "text", "text": "Message 1"}]},
            {"role": "assistant", "content": [{"type": "text", "text": "Message 2"}]},
        ],
    }

    mock_graph_manager.get_historical_messages.return_value = mock_response

    result = await agent_service.get_thread_history(thread_id, page, size)

    assert result is not None
    assert result["pagination"]["thread_id"] == thread_id
    assert result["pagination"]["current_page"] == page
    assert result["pagination"]["total_messages"] == 15
    assert len(result["messages"]) == 2
    assert result["messages"][0]["role"] == "user"
    assert len(result["messages"][0]["content"]) == 1
    assert result["messages"][0]["content"][0]["type"] == "text"
    assert result["messages"][0]["content"][0]["text"] == "Message 1"
    assert result["messages"][1]["role"] == "assistant"
    assert len(result["messages"][1]["content"]) == 1
    assert result["messages"][1]["content"][0]["type"] == "text"
    assert result["messages"][1]["content"][0]["text"] == "Message 2"

    mock_organization_member_thread_repository.get_by_thread_id.assert_called_once_with(
        thread_id
    )
    mock_graph_manager.get_historical_messages.assert_called_once_with(
        thread_id, page, size
    )


@pytest.mark.anyio
async def test_get_thread_history_not_found(
    agent_service,
    mock_organization_member_thread_repository,
):
    thread_id = "non-existent-thread-id"
    page = 1
    size = 10

    mock_organization_member_thread_repository.get_by_thread_id.return_value = None

    with pytest.raises(
        ThreadOwnershipError,
        match="Thread not found or does not belong to the current user",
    ):
        await agent_service.get_thread_history(thread_id, page, size)

    mock_organization_member_thread_repository.get_by_thread_id.assert_called_once_with(
        thread_id
    )


@pytest.mark.anyio
async def test_get_thread_history_wrong_owner(
    agent_service,
    mock_organization_member_thread_repository,
):
    thread_id = "test-thread-id-wrong-owner"
    page = 1
    size = 10
    other_org_member_id = uuid.uuid4()

    assert other_org_member_id != agent_service.org_member_id

    mock_thread = ThreadRead(
        id=thread_id,
        organization_member_id=other_org_member_id,
        crm_account_id="test-crm-account-id",
        created_at=datetime.now(UTC),
        updated_at=datetime.now(UTC),
        name="test-thread",
    )
    mock_organization_member_thread_repository.get_by_thread_id.return_value = (
        mock_thread
    )

    with pytest.raises(
        ThreadOwnershipError,
        match="Thread not found or does not belong to the current user",
    ):
        await agent_service.get_thread_history(thread_id, page, size)

    mock_organization_member_thread_repository.get_by_thread_id.assert_called_once_with(
        thread_id
    )


@pytest.mark.anyio
async def test_get_threads_by_org_member_without_crm_account(
    agent_service, mock_organization_member_thread_repository, org_member_id
):
    thread1 = type(
        "MockThread",
        (),
        {
            "thread_id": "thread-1",
            "organization_member_id": org_member_id,
            "crm_account_id": None,
            "created_at": datetime.now(UTC),
            "updated_at": datetime.now(UTC),
            "thread_name": "thread-2-name",
        },
    )()
    thread2 = type(
        "MockThread",
        (),
        {
            "thread_id": "thread-2",
            "organization_member_id": org_member_id,
            "crm_account_id": None,
            "created_at": datetime.now(UTC),
            "updated_at": datetime.now(UTC),
            "thread_name": "thread-2-name",
        },
    )()
    mock_organization_member_thread_repository.get_by_org_member_without_crm_account.return_value = [
        thread1,
        thread2,
    ]

    result = await agent_service.get_threads_by_org_member_without_crm_account()

    assert result is not None
    assert len(result.threads) == 2
    assert result.threads[0].id == "thread-1"
    assert result.threads[1].id == "thread-2"
    assert result.threads[0].crm_account_id is None
    assert result.threads[1].crm_account_id is None
    mock_organization_member_thread_repository.get_by_org_member_without_crm_account.assert_called_once_with(
        org_member_id
    )


@pytest.mark.anyio
async def test_delete_thread(agent_service, mock_organization_member_thread_repository):
    thread_id = "test-thread-123"

    await agent_service.delete_thread(thread_id)

    mock_organization_member_thread_repository.delete_by_thread_id.assert_called_once_with(
        thread_id
    )


@pytest.mark.anyio
async def test_update_thread_name(
    agent_service, mock_organization_member_thread_repository
):
    thread_id = "test-thread-123"
    thread_name = "Updated Thread Name"

    await agent_service.update_thread_name(thread_id, thread_name)

    mock_organization_member_thread_repository.set_thread_name.assert_called_once_with(
        thread_id, thread_name
    )


@pytest.mark.anyio
async def test_update_thread_crm_account_id_success(
    agent_service, mock_organization_member_thread_repository
):
    thread_id = "test-thread-123"
    crm_account_id = "new-crm-account-456"

    mock_thread = type("MockThread", (), {"crm_account_id": None})()
    mock_organization_member_thread_repository.get_by_thread_id.return_value = (
        mock_thread
    )

    await agent_service.update_thread_crm_account_id(thread_id, crm_account_id)

    mock_organization_member_thread_repository.get_by_thread_id.assert_called_once_with(
        thread_id
    )
    mock_organization_member_thread_repository.set_thread_crm_account_id.assert_called_once_with(
        thread_id, crm_account_id
    )


@pytest.mark.anyio
async def test_update_thread_crm_account_id_already_set(
    agent_service, mock_organization_member_thread_repository
):
    thread_id = "test-thread-123"
    crm_account_id = "new-crm-account-456"

    mock_thread = type("MockThread", (), {"crm_account_id": "existing-crm-account"})()
    mock_organization_member_thread_repository.get_by_thread_id.return_value = (
        mock_thread
    )

    with pytest.raises(ValueError, match="CRM account ID is already set"):
        await agent_service.update_thread_crm_account_id(thread_id, crm_account_id)

    mock_organization_member_thread_repository.get_by_thread_id.assert_called_once_with(
        thread_id
    )
    mock_organization_member_thread_repository.set_thread_crm_account_id.assert_not_called()


@pytest.mark.anyio
async def test_get_actions_returns_properly_typed_actions(
    agent_service,
    mocker,
):
    """Test that get_actions returns properly typed ActionListItem objects"""
    mock_actions = [
        ActionListItem(
            title="Prep my next meeting",
            description="Prepare for a sales meeting",
            prompt="Meeting prompt content",
        ),
        ActionListItem(
            title="Draft a follow-up email",
            description="Draft a follow-up email",
            prompt="Email prompt content",
        ),
        ActionListItem(
            title="Prepare RFP response",
            description="Prepare RFP response",
            prompt="RFP prompt content",
        ),
    ]

    mocker.patch(
        "app.agentic.service.ActionEngine",
        return_value=mocker.AsyncMock(
            get_available_actions=mocker.AsyncMock(return_value=mock_actions)
        ),
    )

    result = await agent_service.get_actions()

    assert len(result) == 3
    assert all(isinstance(action, ActionListItem) for action in result)

    for action in result:
        assert hasattr(action, "title")
        assert hasattr(action, "description")
        assert hasattr(action, "prompt")

    titles = [action.title for action in result]
    assert "Prep my next meeting" in titles
    assert "Draft a follow-up email" in titles
    assert "Prepare RFP response" in titles

    # Test specific action properties
    meeting_action = next(a for a in result if a.title == "Prep my next meeting")
    assert meeting_action.title == "Prep my next meeting"
    assert meeting_action.description == "Prepare for a sales meeting"
    assert meeting_action.prompt == "Meeting prompt content"


@pytest.mark.anyio
async def test_create_account_summary(agent_service, mock_graph_manager):
    request = AccountSummaryRequest(crm_account_id="test-crm-account-id")
    await agent_service.create_account_summary(request)

    mock_graph_manager.invoke_graph.assert_called_once()
