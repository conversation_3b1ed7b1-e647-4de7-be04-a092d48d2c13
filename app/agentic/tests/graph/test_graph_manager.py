import uuid
from unittest.mock import AsyncMock

import pytest
from langchain_core.messages import AIMessageChunk
from langfuse.langchain import Callback<PERSON>andler
from langgraph.checkpoint.memory import InMemorySaver
from langgraph.graph.state import CompiledStateGraph
from langgraph.types import Command, Interrupt

from app.agentic.graph.chat_history_repository import ChatHistoryRepository
from app.agentic.graph.graph_manager import GraphManager


@pytest.fixture
def mock_compiled_graph(mocker):
    mock = mocker.AsyncMock(spec=CompiledStateGraph)
    mock.ainvoke = AsyncMock(return_value={"messages": ["mocked output"]})
    return mock


@pytest.fixture
def mock_langfuse_callback_handler(mocker):
    return mocker.Mock(spec=CallbackHandler)


@pytest.fixture
def graph_manager(
    mock_compiled_graph,
    mock_langfuse_callback_handler,
):
    runner = GraphManager(
        graph=mock_compiled_graph,
        langfuse_callback_handler=mock_langfuse_callback_handler,
        chat_history_repository=ChatHistoryRepository(InMemorySaver()),
    )

    assert runner.graph is mock_compiled_graph

    return runner


@pytest.mark.anyio
async def test_stream_graph(org_id, graph_manager, mocker):
    thread_id = str(uuid.uuid4())
    user_id = uuid.uuid4()
    crm_account_id = "test-crm-account-id"

    graph_input = {
        "messages": ["stream input"],
        "thread_id": thread_id,
        "crm_account_id": crm_account_id,
        "org_id": org_id,
        "user_id": user_id,
    }

    async def mock_astream(*_, **__):
        yield "messages", (AIMessageChunk(content="Hello "), {})
        yield "messages", (AIMessageChunk(content="World!"), {})

    patched_astream = mocker.patch.object(
        graph_manager.graph, "astream", side_effect=mock_astream
    )

    patched_process = mocker.patch.object(
        graph_manager.stream_output_processor,
        "process",
        return_value=AsyncMock(),
    )

    stream_iterator = graph_manager.stream_graph(**graph_input)
    [chunk async for chunk in stream_iterator]  # Consume the stream
    patched_astream.assert_called_once()

    _, call_kwargs = patched_astream.call_args

    assert "input" in call_kwargs
    assert call_kwargs["input"] == graph_input
    assert "config" in call_kwargs
    config = call_kwargs["config"]
    assert config["configurable"]["thread_id"] == thread_id
    assert config["configurable"]["checkpoint_ns"] == ""
    assert config["callbacks"] == [graph_manager.langfuse_callback_handler]

    assert patched_process.call_count == 2
    patched_process.assert_has_calls(
        [
            mocker.call(AIMessageChunk(content="Hello ")),
            mocker.call(AIMessageChunk(content="World!")),
        ]
    )


@pytest.mark.anyio
async def test_stream_graph_with_interrupt(org_id, graph_manager, mocker):
    thread_id = str(uuid.uuid4())
    user_id = uuid.uuid4()
    crm_account_id = "test-crm-account-id"

    graph_input = {
        "messages": ["stream input"],
        "thread_id": thread_id,
        "crm_account_id": crm_account_id,
        "org_id": org_id,
        "user_id": user_id,
    }

    interrupt = Interrupt(value="test_interrupt", resumable=True, ns="test_ns")

    async def mock_astream(*_, **__):
        yield "updates", {"__interrupt__": (interrupt, "Interrupted")}

    patched_astream_events = mocker.patch.object(
        graph_manager.graph, "astream", side_effect=mock_astream
    )

    patched_process = mocker.patch.object(
        graph_manager.stream_output_processor,
        "process",
        return_value=AsyncMock(),
    )

    stream_iterator = graph_manager.stream_graph(**graph_input)
    [chunk async for chunk in stream_iterator]  # Consume the stream

    patched_astream_events.assert_called_once()

    patched_process.assert_called_once_with(interrupt)


@pytest.mark.anyio
async def test_stream_graph_with_command(org_id, graph_manager, mocker):
    thread_id = str(uuid.uuid4())
    user_id = uuid.uuid4()
    crm_account_id = "test-crm-account-id"

    graph_input = {
        "messages": ["stream input"],
        "thread_id": thread_id,
        "crm_account_id": crm_account_id,
        "org_id": org_id,
        "user_id": user_id,
    }

    async def mock_astream(*_, **__):
        yield "messages", (Command(update="Command update message"), {})

    patched_astream = mocker.patch.object(
        graph_manager.graph, "astream", side_effect=mock_astream
    )

    patched_process = mocker.patch.object(
        graph_manager.stream_output_processor,
        "process",
        return_value=AsyncMock(),
    )

    stream_iterator = graph_manager.stream_graph(**graph_input)
    [chunk async for chunk in stream_iterator]  # Consume the stream

    patched_astream.assert_called_once()

    patched_process.assert_called_once_with(Command(update="Command update message"))
