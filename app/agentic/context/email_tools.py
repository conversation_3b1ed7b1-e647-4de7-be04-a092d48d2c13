from typing import Any
from uuid import <PERSON><PERSON><PERSON>

from pydantic import BaseModel

from app.agentic.context.base import IToolBuilder
from app.agentic.context.schemas import (
    ListEmailLabels,
    ListEmailThreads,
    ReadEmailThread,
    SearchEmailsByContext,
    ToolDefinition,
)
from app.core.database import Async<PERSON><PERSON><PERSON>Local
from app.integrations.schemas import EmailMessage, EmailThread
from app.workspace.integrations.user_integrations import (
    UserIntegrations,
    create_user_integrations,
)


class EmailToolBuilder(IToolBuilder):
    EMAIL_METHODS: list[tuple[str, str, type[BaseModel], bool]] = [
        (
            "list_email_labels",
            "List email labels with optional filtering",
            ListEmailLabels,
            False,
        ),
        (
            "list_email_threads",
            "List email threads with intelligent filtering based on query, company, client, etc.",
            ListEmailThreads,
            False,
        ),
        (
            "read_email_thread",
            "Read the content of an email thread",
            ReadEmailThread,
            False,
        ),
        (
            "search_emails_by_context",
            "Search emails by company/client context with advanced filtering",
            SearchEmailsByContext,
            <PERSON>als<PERSON>,
        ),
    ]

    def __init__(self, user_id: UUID, user_integrations: UserIntegrations):
        self.user_id = user_id
        self.user_integrations = user_integrations
        self.environment_id = user_integrations.environment.id

    async def build_tools(self) -> list[ToolDefinition]:
        delegator = _EmailDelegator(self.user_id, self.environment_id)

        return [
            ToolDefinition(
                name=method_name,
                coroutine=getattr(delegator, method_name),
                description=description,
                args_schema=schema_class,
                requires_human_review=requires_human_review,
            )
            for method_name, description, schema_class, requires_human_review in self.EMAIL_METHODS
        ]


class _EmailDelegator:
    def __init__(self, user_id: UUID, environment_id: UUID):
        self.user_id = user_id
        self.environment_id = environment_id

    async def list_email_labels(self, name_filter: str = "") -> list[dict[str, Any]]:
        async with AsyncSessionLocal() as session:
            user_integrations = await create_user_integrations(
                user_id=self.user_id,
                environment_id=self.environment_id,
                db_session=session,
            )

            email = await user_integrations.email()
            if not email:
                raise RuntimeError(f"No email integration for user {self.user_id}")

            return await email.list_filtered_labels(
                name_filter=name_filter if name_filter else None
            )

    async def list_email_threads(
        self,
        query: str = "",
        max_results: int = 50,
        include_spam_trash: bool = False,
        company_filter: str = "",
        client_filter: str = "",
        unread_only: bool = False,
    ) -> list[EmailThread]:
        async with AsyncSessionLocal() as session:
            user_integrations = await create_user_integrations(
                user_id=self.user_id,
                environment_id=self.environment_id,
                db_session=session,
            )

            email = await user_integrations.email()
            if not email:
                raise RuntimeError(f"No email integration for user {self.user_id}")

            return await email.list_threads_with_filters(
                query=query if query else None,
                company_filter=company_filter if company_filter else None,
                client_filter=client_filter if client_filter else None,
                unread_only=unread_only,
                max_results=max_results,
                include_spam_trash=include_spam_trash,
            )

    async def read_email_thread(
        self, thread_id: str, max_messages: int = 10
    ) -> EmailThread:
        async with AsyncSessionLocal() as session:
            user_integrations = await create_user_integrations(
                user_id=self.user_id,
                environment_id=self.environment_id,
                db_session=session,
            )

            email = await user_integrations.email()
            if not email:
                raise RuntimeError(f"No email integration for user {self.user_id}")

            return await email.get_thread_with_limit(
                thread_id=thread_id, max_messages=max_messages if max_messages else None
            )

    async def search_emails_by_context(
        self,
        company_name: str = "",
        client_name: str = "",
        domain: str = "",
        subject_keywords: str = "",
        date_range_days: int = 30,
        max_results: int = 20,
    ) -> list[EmailMessage]:
        async with AsyncSessionLocal() as session:
            user_integrations = await create_user_integrations(
                user_id=self.user_id,
                environment_id=self.environment_id,
                db_session=session,
            )

            email = await user_integrations.email()
            if not email:
                raise RuntimeError(f"No email integration for user {self.user_id}")

            return await email.search_emails_by_context(
                company_name=company_name if company_name else None,
                client_name=client_name if client_name else None,
                domain=domain if domain else None,
                subject_keywords=subject_keywords if subject_keywords else None,
                date_range_days=date_range_days if date_range_days else None,
                max_results=max_results,
            )
