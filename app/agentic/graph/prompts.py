import json
from datetime import date, datetime
from enum import Enum
from typing import Any

from langchain_core.messages import HumanMessage, SystemMessage
from langchain_core.prompts import Chat<PERSON>romptTemplate, MessagesPlaceholder
from langfuse import <PERSON><PERSON>

from app.common.helpers.to_async import to_async


class LangfusePrompt(str, Enum):
    ACCOUNT_SUMMARY = "account_summary"
    ACCOUNT_CONTEXT = "account_context"
    SUPERVISOR = "supervisor"
    SALES_DOCUMENT_AGENT = "sales_document_agent"


class AgentPrompts:
    def __init__(self, langfuse_client: Langfuse):
        self.langfuse = langfuse_client

    @staticmethod
    def _create_prompt_template(system_message: str, name: str) -> ChatPromptTemplate:
        return ChatPromptTemplate.from_messages(
            [
                SystemMessage(content=system_message, name=name),
                MessagesPlaceholder(variable_name="messages"),
            ]
        )

    @to_async
    def _get_prompt_string(self, prompt_name: <PERSON><PERSON><PERSON>rompt) -> str:
        prompt_raw = self.langfuse.get_prompt(prompt_name.value)
        return prompt_raw.get_langchain_prompt()

    async def get_supervisor_system_prompt(self) -> ChatPromptTemplate:
        prompt = await self._get_prompt_string(LangfusePrompt.SUPERVISOR)
        return self._create_prompt_template(prompt, "supervisor_prompt")

    async def get_sales_document_agent_prompt(self) -> ChatPromptTemplate:
        prompt = await self._get_prompt_string(LangfusePrompt.SALES_DOCUMENT_AGENT)
        return self._create_prompt_template(prompt, "sales_document_agent_prompt")

    async def format_account_context_message(
        self, account_info: dict[str, Any], langfuse_prompt: LangfusePrompt
    ) -> SystemMessage | HumanMessage:
        prompt = await self._get_prompt_string(langfuse_prompt)
        formatted_account_info = self._safe_json_dumps(account_info, indent=2)
        final_content = prompt.format(account_info=formatted_account_info)

        if langfuse_prompt is LangfusePrompt.ACCOUNT_SUMMARY:
            return HumanMessage(content=final_content, name="account_summary_prompt")

        return SystemMessage(content=final_content, name="account_context_prompt")

    def _serialize_datetime_objects(self, obj: Any) -> Any:
        if isinstance(obj, datetime):
            return obj.isoformat()
        elif isinstance(obj, date):
            return obj.isoformat()
        elif isinstance(obj, dict):
            return {
                key: self._serialize_datetime_objects(value)
                for key, value in obj.items()
            }
        elif isinstance(obj, list):
            return [self._serialize_datetime_objects(item) for item in obj]
        else:
            return obj

    def _safe_json_dumps(self, obj: Any, **kwargs) -> str:
        serialized_obj = self._serialize_datetime_objects(obj)
        return json.dumps(serialized_obj, **kwargs)
