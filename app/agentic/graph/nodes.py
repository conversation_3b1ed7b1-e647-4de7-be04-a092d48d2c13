from datetime import UTC, datetime

from langchain_core.messages import BaseMessage, HumanMessage, SystemMessage
from langchain_google_genai import ChatGoogleGenerativeA<PERSON>

from app.agentic.context.account import get_account_details, get_comprehensive_crm_data
from app.agentic.graph.prompts import AgentPrompts, LangfusePrompt
from app.agentic.graph.state import ConversationState
from app.common.helpers.logger import get_logger
from app.workspace.integrations.user_integrations import UserIntegrations

logger = get_logger()


async def fetch_account_node(
    state: ConversationState,
    user_integrations: UserIntegrations,
) -> ConversationState:
    account_details = await get_account_details(
        state["user_id"],
        state["crm_account_id"],
        user_integrations,
    )

    state["account_info"] = account_details
    state["last_refetch_at"] = datetime.now(UTC)
    return state


async def context_injector_node(
    state: ConversationState, agent_prompts: AgentPrompts
) -> ConversationState:
    account_info = state.get("account_info")
    if not account_info:
        return state

    if any(msg.name == "account_context" for msg in state["messages"]):
        return state

    context_message = await agent_prompts.format_account_context_message(
        account_info, LangfusePrompt.ACCOUNT_CONTEXT
    )
    new_messages: list[BaseMessage] = [context_message, *state["messages"]]

    state["messages"] = new_messages
    return state


async def file_search_context_node(
    state: ConversationState,
    user_integrations: UserIntegrations,
) -> ConversationState:
    """
    Search for relevant documents and add them as context for the supervisor.
    This runs BEFORE the supervisor so it has document context when making decisions.
    """
    # Get the latest user message
    latest_user_message = state["messages"][-1]
    if not isinstance(latest_user_message, HumanMessage):
        return state

    query = str(latest_user_message.content)

    try:
        # Search for relevant documents
        results = await user_integrations.search_files(query, limit=3)

        if results:
            # Create a system message with the search results
            documents_text = "\n\n".join([f"Document: {doc}" for doc in results])
            file_search_message = SystemMessage(
                content=f"Relevant documents found for your query '{query}':\n\n{documents_text}\n\nUse this information to help answer and/or format the user's question.",
                name="file_search_context",
            )

            # Add the file search results to the beginning of the message list
            new_messages = [file_search_message, *state["messages"]]
            state["messages"] = new_messages

    except Exception as e:
        logger.exception(f"Failed to search files: {e}")

    return state


async def summarize_account_node(
    state: ConversationState,
    llm: ChatGoogleGenerativeAI,
    agent_prompts: AgentPrompts,
    user_integrations: UserIntegrations,
) -> ConversationState:
    account_info = state.get("account_info", {})
    opportunities_info = state.get("opportunities_info", [])
    contacts_info = state.get("contacts_info", [])

    if not any([account_info, opportunities_info, contacts_info]):
        return state

    comprehensive_data = {
        "account": account_info,
        "opportunities": opportunities_info,
        "contacts": contacts_info,
    }

    context_message = await agent_prompts.format_account_context_message(
        comprehensive_data, LangfusePrompt.ACCOUNT_SUMMARY
    )
    summary = await llm.ainvoke(context_message.content)

    try:
        crm_provider = await user_integrations.crm()
        crm_user_id = await user_integrations.crm_user_id()

        if crm_provider and crm_user_id:
            summary_text = (
                str(summary.content) if hasattr(summary, "content") else str(summary)
            )

            await crm_provider.store_account_summary(
                user_id=crm_user_id,
                crm_account_id=state["crm_account_id"],
                summary=summary_text,
            )
            logger.info(
                f"Successfully stored summary for account {state['crm_account_id']}"
            )
        else:
            logger.warning("No CRM provider or user ID available to store summary")
    except Exception as e:
        logger.exception(f"Failed to store account summary: {e}")

    return state


async def fetch_comprehensive_crm_data_node(
    state: ConversationState,
    user_integrations: UserIntegrations,
) -> ConversationState:
    try:
        (
            account_info,
            opportunities_info,
            contacts_info,
        ) = await get_comprehensive_crm_data(
            state["user_id"],
            state["crm_account_id"],
            user_integrations,
        )

        state["account_info"] = account_info
        state["opportunities_info"] = opportunities_info
        state["contacts_info"] = contacts_info
        state["last_refetch_at"] = datetime.now(UTC)

    except Exception as e:
        logger.exception(f"Failed to fetch comprehensive CRM data: {e}")

    return state
