import uuid
from typing import Any

from langchain_core.messages import AIMessage, HumanMessage
from langfuse import Lang<PERSON>
from langfuse._client.datasets import DatasetItemClient
from langfuse.langchain import CallbackHandler
from langgraph.checkpoint.memory import InMemorySaver

from app.agentic.graph.chat_history_repository import ChatHistoryRepository
from app.agentic.graph.graph import GraphFactory, GraphType
from app.agentic.graph.graph_manager import GraphManager
from app.common.helpers.logger import get_logger
from app.core.config import config
from app.core.database import AsyncSessionLocal
from app.workspace.integrations.user_integrations import (
    create_user_integrations,
)
from app.workspace.repositories.environment import EnvironmentRepository
from app.workspace.repositories.organization import (
    OrganizationRepository,
)
from app.workspace.schemas import OrgEnvironment
from app.workspace.services.organization import OrganizationService
from app.workspace.types import EnvironmentType

logger = get_logger()


class LangfuseEvaluationPipeline:
    def __init__(
        self,
        dataset_name: str,
        user_id: uuid.UUID,
        org_id: uuid.UUID,
        crm_account_id: str,
        env_type: EnvironmentType,
        experiment_name: str,
        experiment_description: str,
    ):
        self.dataset_name = dataset_name
        self.org_id = org_id
        self.user_id = user_id
        self.crm_account_id = crm_account_id
        self.env_type = env_type
        self.experiment_name = experiment_name
        self.experiment_description = experiment_description

        self.langfuse_client = Langfuse(
            public_key=config.langfuse_public_key,
            secret_key=config.langfuse_secret_key,
            host=config.langfuse_host,
            environment="evaluation",
        )
        self.langfuse_callback_handler = CallbackHandler()

        self.db_session = AsyncSessionLocal()

        org_repo = OrganizationRepository(self.db_session)
        env_repo = EnvironmentRepository(self.db_session)

        self.org_service = OrganizationService(
            db_session=self.db_session, org_repo=org_repo, env_repo=env_repo
        )
        self.environment: OrgEnvironment | None = None

    async def _initialize(self) -> None:
        if self.environment is None:
            self.environment = await self._get_environment()

            if self.environment is None:
                raise RuntimeError("Failed to initialize environment")

    async def _get_environment(self) -> OrgEnvironment:
        environment = await self.org_service.get_env(
            org_id=self.org_id, env_type=self.env_type
        )
        if not environment:
            raise RuntimeError(
                f"No environment found for org {self.org_id} and type {self.env_type}"
            )
        return environment

    async def run_evaluation(self) -> None:
        logger.info(f"Starting Langfuse evaluation for dataset {self.dataset_name}")
        graph_manager = await self._create_graph_manager()
        dataset = self.langfuse_client.get_dataset(self.dataset_name)

        for item in dataset.items:
            try:
                logger.info("--" * 80)
                logger.info(f"Evaluating item {item.id} with input: {item.input}")
                await self._process_and_evaluate_item(item, graph_manager)
            except Exception:
                logger.exception(
                    f"Error evaluating item {item.id} for dataset {self.dataset_name}"
                )

        logger.info(f"Evaluation finished for dataset {self.dataset_name}.")

    async def _create_graph_manager(self) -> GraphManager:
        if self.environment is None:
            raise RuntimeError("Failed to initialize environment")

        user_integrations = await create_user_integrations(
            user_id=self.user_id,
            environment_id=self.environment.id,
            db_session=self.db_session,
        )

        graph_factory = GraphFactory(
            self.user_id, user_integrations, self.langfuse_client
        )

        # Bypass the graph manager factory like a thug, will refactor
        graph_definition = await graph_factory.create(GraphType.CONVERSATIONAL)
        compiled_graph = graph_definition.compile(checkpointer=InMemorySaver())

        return GraphManager(
            graph=compiled_graph,
            langfuse_callback_handler=self.langfuse_callback_handler,
            chat_history_repository=ChatHistoryRepository(checkpointer=InMemorySaver()),
        )

    async def _process_and_evaluate_item(
        self, item: DatasetItemClient, graph_manager: GraphManager
    ) -> None:
        with item.run(
            run_name=self.experiment_name, run_description=self.experiment_description
        ) as root_span:
            graph_input = {
                "messages": [HumanMessage(item.input)],
                "crm_account_id": self.crm_account_id,
                "thread_id": None,
                "resume": False,
                "org_id": self.org_id,
                "user_id": self.user_id,
            }

            graph_response = await graph_manager.invoke_graph(graph_input)
            agent_answer = self._extract_final_answer(graph_response)
            root_span.update_trace(input=item.input, output=agent_answer)

    @staticmethod
    def _extract_final_answer(graph_response: dict[str, Any]) -> str:
        messages = graph_response.get("messages", [])
        for message in reversed(messages):
            if isinstance(message, AIMessage):
                content = message.content
                if isinstance(content, str):
                    return content
                if isinstance(content, list):
                    text_parts = [
                        part["text"]
                        for part in content
                        if isinstance(part, dict) and "text" in part
                    ]
                    return "".join(text_parts)
                return ""
        return ""

    async def _close(self):
        if self.db_session:
            await self.db_session.close()

    async def __aenter__(self):
        await self._initialize()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self._close()
        self.langfuse_client.shutdown()
