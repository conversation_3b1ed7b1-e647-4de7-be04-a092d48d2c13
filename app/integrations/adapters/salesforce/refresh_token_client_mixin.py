from simple_salesforce.exceptions import SalesforceExpiredSession

from app.common.helpers.logger import get_logger
from app.integrations.adapters.salesforce.client import SalesforceClient
from app.integrations.base.credentials_resolver import ICredentials
from app.integrations.base.oauth_refresh_mixin import BaseOAuthRefreshTokenMixin

logger = get_logger()


class SalesforceRefreshTokenClientMixin(BaseOAuthRefreshTokenMixin):
    credentials: ICredentials
    salesforce_client: SalesforceClient

    def get_integration_specific_auth_exceptions(self) -> tuple[type, ...]:
        return (SalesforceExpiredSession,)

    def get_client_cache_attribute_name(self) -> str:
        return "_lazy_client"

    async def refresh_integration_token(self):
        logger.info("Refreshing Salesforce token")
        self.credentials = await self.credentials.refresh_token()
        logger.info("Re-initializing Salesforce client with refreshed token")
        self.reinitialize_client()

    def reinitialize_client(self):
        # Clear any cached client instance so it gets recreated with new credentials
        if hasattr(self.salesforce_client, self.get_client_cache_attribute_name()):
            delattr(self.salesforce_client, self.get_client_cache_attribute_name())
        self.init_salesforce_client(self.credentials)

    def init_salesforce_client(self, credentials: ICredentials):
        self.credentials = credentials

        if (
            "access_token" not in self.credentials.secrets
            or "instance_url" not in self.credentials.secrets
        ):
            error_msg = "Missing required 'access_token' or 'instance_url' for Salesforce OAuth authentication"
            logger.exception(error_msg)
            raise ValueError(error_msg)

        self.salesforce_client = SalesforceClient(
            instance_url=self.credentials.secrets["instance_url"],
            access_token=self.credentials.secrets["access_token"],
        )
