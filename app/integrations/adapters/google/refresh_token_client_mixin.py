from typing import Generic, TypeVar, Union

from googleapiclient.errors import HttpError

from app.common.helpers.logger import get_logger
from app.integrations.adapters.gmail.client import GmailClient
from app.integrations.adapters.google_calendar.client import GoogleCalendarClient
from app.integrations.base.credentials_resolver import ICredentials
from app.integrations.base.oauth_refresh_mixin import BaseOAuthRefreshTokenMixin
from app.integrations.types import IntegrationSource

logger = get_logger()

ClientType = TypeVar("ClientType", bound=Union[GmailClient, GoogleCalendarClient])


class GoogleRefreshTokenClientMixin(BaseOAuthRefreshTokenMixin, Generic[ClientType]):
    """Generic mixin for Google handlers to provide token refresh capabilities with proper typing."""

    credentials: ICredentials
    google_client: ClientType
    integration_source: IntegrationSource

    def get_integration_specific_auth_exceptions(self) -> tuple[type, ...]:
        return (HttpError,)

    def get_client_cache_attribute_name(self) -> str:
        return "_service"

    async def refresh_integration_token(self):
        logger.info(f"Refreshing {self.integration_source.value} token")
        self.credentials = await self.credentials.refresh_token()
        logger.info(
            f"Re-initializing {self.integration_source.value} client with refreshed token"
        )
        self.reinitialize_client()

    def reinitialize_client(self):
        self.init_google_client(self.credentials, self.integration_source)

    def init_google_client(
        self, credentials: ICredentials, integration_source: IntegrationSource
    ):
        self.credentials = credentials
        self.integration_source = integration_source

        if (
            "access_token" not in self.credentials.secrets
            or "refresh_token" not in self.credentials.secrets
        ):
            error_msg = f"Missing required 'access_token' or 'refresh_token' for {integration_source.value} OAuth authentication"
            logger.exception(error_msg)
            raise ValueError(error_msg)

        logger.debug(f"Using OAuth-based authentication for {integration_source.value}")

        # Create the client based on the integration source
        self.google_client = self._create_client(
            self.credentials.secrets, integration_source
        )

    def _create_client(
        self, client_params: dict, integration_source: IntegrationSource
    ) -> ClientType:
        match integration_source:
            case IntegrationSource.GMAIL:
                return GmailClient(client_params)  # type: ignore[return-value]
            case IntegrationSource.GOOGLE_CALENDAR:
                return GoogleCalendarClient(client_params)  # type: ignore[return-value]
            case _:
                raise ValueError(
                    f"Unsupported integration source: {integration_source}"
                )
