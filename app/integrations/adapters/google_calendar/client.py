from typing import Any

from googleapiclient.discovery import build

from app.common.helpers.logger import get_logger
from app.common.helpers.to_async import to_async
from app.integrations.base.google_client import BaseGoogleClient

logger = get_logger()


class GoogleCalendarClientError(Exception):
    pass


class GoogleCalendarClient(BaseGoogleClient):
    def __init__(self, credentials: dict[str, Any]):
        super().__init__(credentials)
        self._service = self._build_service()

    def _build_service(self):
        try:
            return build("calendar", "v3", credentials=self.credentials)
        except Exception as e:
            raise GoogleCalendarClientError(
                f"Failed to build Calendar service: {str(e)}"
            ) from e

    @to_async
    def list_calendars(self) -> dict[str, Any]:
        try:
            return self._service.calendarList().list().execute()
        except Exception as e:
            logger.exception("Failed to list calendars")
            raise GoogleCalendarClientError(
                f"Failed to list calendars: {str(e)}"
            ) from e

    @to_async
    def get_calendar(self, calendar_id: str) -> dict[str, Any]:
        try:
            return self._service.calendarList().get(calendarId=calendar_id).execute()
        except Exception as e:
            logger.exception(f"Failed to get calendar {calendar_id}")
            raise GoogleCalendarClientError(f"Failed to get calendar: {str(e)}") from e

    @to_async
    def list_events(
        self,
        calendar_id: str,
        time_min: str | None = None,
        time_max: str | None = None,
        max_results: int = 250,
        single_events: bool = True,
        order_by: str = "startTime",
        show_deleted: bool = False,
        page_token: str | None = None,
    ) -> dict[str, Any]:
        try:
            params = {
                "calendarId": calendar_id,
                "maxResults": max_results,
                "singleEvents": single_events,
                "orderBy": order_by,
                "showDeleted": show_deleted,
            }

            if time_min:
                params["timeMin"] = time_min
            if time_max:
                params["timeMax"] = time_max
            if page_token:
                params["pageToken"] = page_token

            return self._service.events().list(**params).execute()
        except Exception as e:
            logger.exception(f"Failed to list events for calendar {calendar_id}")
            raise GoogleCalendarClientError(f"Failed to list events: {str(e)}") from e

    @to_async
    def get_event(self, calendar_id: str, event_id: str) -> dict[str, Any]:
        try:
            return (
                self._service.events()
                .get(calendarId=calendar_id, eventId=event_id)
                .execute()
            )
        except Exception as e:
            logger.exception(
                f"Failed to get event {event_id} from calendar {calendar_id}"
            )
            raise GoogleCalendarClientError(f"Failed to get event: {str(e)}") from e

    @to_async
    def get_user_info(self) -> dict[str, Any]:
        try:
            return self._service.calendarList().get(calendarId="primary").execute()
        except Exception as e:
            logger.exception("Failed to get user info")
            raise GoogleCalendarClientError(f"Failed to get user info: {str(e)}") from e
