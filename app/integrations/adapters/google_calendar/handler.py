from datetime import datetime
from typing import Any

from app.common.helpers.logger import get_logger
from app.integrations.adapters.google.refresh_token_client_mixin import (
    GoogleRefreshTokenClientMixin,
)
from app.integrations.adapters.google_calendar.client import GoogleCalendarClient
from app.integrations.adapters.google_calendar.utils import (
    convert_google_calendar_to_calendar,
    convert_google_event_to_calendar_event,
)
from app.integrations.base.credentials_resolver import ICredentials
from app.integrations.types import IntegrationSource

logger = get_logger()


class GoogleCalendarHandler(GoogleRefreshTokenClientMixin[GoogleCalendarClient]):
    """Google Calendar business logic handler with token refresh capabilities."""

    def __init__(self, credentials: ICredentials):
        # Validate that required Google Calendar credentials are present
        if "client_id" not in credentials.secrets:
            raise ValueError("Google Calendar client_id not found")
        self.init_google_client(credentials, IntegrationSource.GOOGLE_CALENDAR)

    @GoogleRefreshTokenClientMixin.handle_expired_session
    async def get_user_info(self) -> dict[str, Any]:
        """Get user profile information."""
        return await self.google_client.get_user_info()

    @GoogleRefreshTokenClientMixin.handle_expired_session
    async def list_calendars(self) -> list[dict[str, Any]]:
        """List all calendars with business logic."""
        response = await self.google_client.list_calendars()
        calendars = []

        for calendar_data in response.get("items", []):
            calendar = convert_google_calendar_to_calendar(calendar_data)
            calendars.append(calendar)

        return calendars

    @GoogleRefreshTokenClientMixin.handle_expired_session
    async def get_calendar(self, calendar_id: str) -> dict[str, Any]:
        """Get a specific calendar by ID."""
        calendar_data = await self.google_client.get_calendar(calendar_id)
        return convert_google_calendar_to_calendar(calendar_data)

    @GoogleRefreshTokenClientMixin.handle_expired_session
    async def list_events(
        self,
        calendar_id: str,
        start_time: datetime | None = None,
        end_time: datetime | None = None,
        max_results: int = 250,
        single_events: bool = True,
        order_by: str = "startTime",
        show_deleted: bool = False,
        page_token: str | None = None,
    ) -> dict[str, Any]:
        """List events for a calendar with business logic."""
        time_min = start_time.isoformat() if start_time else None
        time_max = end_time.isoformat() if end_time else None

        response = await self.google_client.list_events(
            calendar_id=calendar_id,
            time_min=time_min,
            time_max=time_max,
            max_results=max_results,
            single_events=single_events,
            order_by=order_by,
            show_deleted=show_deleted,
            page_token=page_token,
        )

        events = []
        for event_data in response.get("items", []):
            event = convert_google_event_to_calendar_event(event_data)
            event["calendar_id"] = calendar_id
            events.append(event)

        return {
            "events": events,
            "next_page_token": response.get("nextPageToken"),
        }

    @GoogleRefreshTokenClientMixin.handle_expired_session
    async def get_event(self, calendar_id: str, event_id: str) -> dict[str, Any]:
        """Get a specific event by ID."""
        event_data = await self.google_client.get_event(calendar_id, event_id)
        event = convert_google_event_to_calendar_event(event_data)
        event["calendar_id"] = calendar_id
        return event

    @GoogleRefreshTokenClientMixin.handle_expired_session
    async def search_events(
        self,
        calendar_id: str,
        query: str,
        start_time: datetime | None = None,
        end_time: datetime | None = None,
        max_results: int = 250,
        order_by: str = "startTime",
    ) -> list[dict[str, Any]]:
        """Search events with business logic for filtering."""
        events_response = await self.list_events(
            calendar_id=calendar_id,
            start_time=start_time,
            end_time=end_time,
            max_results=max_results,
            order_by=order_by,
        )

        events = events_response["events"]
        query_lower = query.lower()

        # Business logic: filter events based on query
        filtered_events = []
        for event in events:
            if (
                query_lower in event["title"].lower()
                or (
                    event["description"] and query_lower in event["description"].lower()
                )
                or (event["location"] and query_lower in event["location"].lower())
            ):
                filtered_events.append(event)

        return filtered_events
