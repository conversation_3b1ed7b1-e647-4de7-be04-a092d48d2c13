from datetime import datetime
from typing import Any

from app.integrations.adapters.google_calendar.handler import GoogleCalendarHandler
from app.integrations.base.calendar_adapter import BaseCalendarAdapter
from app.integrations.base.credentials_resolver import ICredentials
from app.integrations.types import IntegrationSource


class GoogleCalendarAdapter(BaseCalendarAdapter):
    """Google Calendar adapter that delegates to GoogleCalendarHandler for business logic."""

    def __init__(self, credentials: ICredentials):
        super().__init__(credentials)
        self._handler = GoogleCalendarHandler(credentials)

    @property
    def source(self) -> IntegrationSource:
        return IntegrationSource.GOOGLE_CALENDAR

    async def list_calendars(self) -> list[dict[str, Any]]:
        return await self._handler.list_calendars()

    async def get_calendar(self, calendar_id: str) -> dict[str, Any]:
        return await self._handler.get_calendar(calendar_id)

    async def list_events(
        self,
        calendar_id: str,
        start_time: datetime | None = None,
        end_time: datetime | None = None,
        max_results: int = 250,
        single_events: bool = True,
        order_by: str = "startTime",
        show_deleted: bool = False,
        page_token: str | None = None,
    ) -> dict[str, Any]:
        return await self._handler.list_events(
            calendar_id=calendar_id,
            start_time=start_time,
            end_time=end_time,
            max_results=max_results,
            single_events=single_events,
            order_by=order_by,
            show_deleted=show_deleted,
            page_token=page_token,
        )

    async def get_event(self, calendar_id: str, event_id: str) -> dict[str, Any]:
        return await self._handler.get_event(calendar_id, event_id)

    async def search_events(
        self,
        calendar_id: str,
        query: str,
        start_time: datetime | None = None,
        end_time: datetime | None = None,
        max_results: int = 250,
        order_by: str = "startTime",
    ) -> list[dict[str, Any]]:
        return await self._handler.search_events(
            calendar_id=calendar_id,
            query=query,
            start_time=start_time,
            end_time=end_time,
            max_results=max_results,
            order_by=order_by,
        )

    async def get_user_info(self) -> dict[str, Any]:
        return await self._handler.get_user_info()
