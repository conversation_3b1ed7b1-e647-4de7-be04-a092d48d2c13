import base64
from datetime import datetime
from typing import Any

from app.common.helpers.logger import get_logger
from app.integrations.schemas import EmailAttachment, EmailMessage, EmailThread

logger = get_logger()


def convert_gmail_message_to_email_message(
    message_data: dict[str, Any],
) -> EmailMessage:
    headers = {}
    payload = message_data.get("payload", {})

    # Extract headers
    for header in payload.get("headers", []):
        headers[header["name"].lower()] = header["value"]

    # Extract body
    body = extract_message_body(payload)

    # Extract attachments
    attachments = extract_attachments(payload)

    # Parse date
    internal_date = message_data.get("internalDate")
    date = datetime.fromtimestamp(int(internal_date) / 1000) if internal_date else None

    return EmailMessage(
        id=message_data["id"],
        thread_id=message_data["threadId"],
        subject=headers.get("subject", ""),
        sender=headers.get("from", ""),
        recipients=headers.get("to", ""),
        cc=headers.get("cc"),
        bcc=headers.get("bcc"),
        date=date,
        body=body,
        snippet=message_data.get("snippet", ""),
        labels=message_data.get("labelIds", []),
        attachments=attachments,
        size_estimate=message_data.get("sizeEstimate", 0),
        raw_data=message_data,
    )


def convert_gmail_thread_to_email_thread(thread_data: dict[str, Any]) -> EmailThread:
    messages = []

    for message_data in thread_data.get("messages", []):
        message = convert_gmail_message_to_email_message(message_data)
        messages.append(message)

    return EmailThread(
        id=thread_data["id"],
        snippet=thread_data.get("snippet", ""),
        messages=messages,
        labels=thread_data.get("labelIds", []),
        history_id=thread_data.get("historyId"),
    )


def extract_message_body(payload: dict[str, Any]) -> str:
    body = ""

    if "parts" in payload:
        for part in payload["parts"]:
            body += extract_part_body(part)
    else:
        body = extract_part_body(payload)

    return body


def extract_part_body(part: dict[str, Any]) -> str:
    mime_type = part.get("mimeType", "")
    body_data = part.get("body", {}).get("data", "")

    if mime_type == "text/plain" and body_data:
        return decode_base64url(body_data)

    # Handle nested parts
    if "parts" in part:
        body = ""
        for subpart in part["parts"]:
            body += extract_part_body(subpart)
        return body

    return ""


def extract_attachments(payload: dict[str, Any]) -> list[EmailAttachment]:
    attachments = []

    if "parts" in payload:
        for part in payload["parts"]:
            attachments.extend(extract_part_attachments(part))
    else:
        attachments.extend(extract_part_attachments(payload))

    return attachments


def extract_part_attachments(part: dict[str, Any]) -> list[EmailAttachment]:
    attachments = []

    # Check if this part is an attachment
    if part.get("filename") and part.get("body", {}).get("attachmentId"):
        attachment = EmailAttachment(
            id=part["body"]["attachmentId"],
            filename=part["filename"],
            mime_type=part.get("mimeType", ""),
            size=part.get("body", {}).get("size", 0),
        )
        attachments.append(attachment)

    # Check nested parts
    if "parts" in part:
        for subpart in part["parts"]:
            attachments.extend(extract_part_attachments(subpart))

    return attachments


def create_gmail_query(
    sender: str | None = None,
    recipient: str | None = None,
    subject: str | None = None,
    has_attachment: bool | None = None,
    is_unread: bool | None = None,
    in_folder: str | None = None,
    after_date: datetime | None = None,
    before_date: datetime | None = None,
    query_text: str | None = None,
) -> str:
    query_parts = []

    if sender:
        query_parts.append(f"from:{sender}")

    if recipient:
        query_parts.append(f"to:{recipient}")

    if subject:
        query_parts.append(f"subject:{subject}")

    if has_attachment:
        query_parts.append("has:attachment")

    if is_unread:
        query_parts.append("is:unread")

    if in_folder:
        query_parts.append(f"in:{in_folder}")

    if after_date:
        query_parts.append(f"after:{after_date.strftime('%Y/%m/%d')}")

    if before_date:
        query_parts.append(f"before:{before_date.strftime('%Y/%m/%d')}")

    if query_text:
        query_parts.append(query_text)

    return " ".join(query_parts)


def decode_base64url(data: str) -> str:
    try:
        return base64.urlsafe_b64decode(data + "===").decode("utf-8")
    except Exception as e:
        logger.warning(f"Error decoding base64 data: {e}")
        return data
