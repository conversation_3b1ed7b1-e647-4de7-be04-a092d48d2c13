from datetime import datetime
from typing import Any

from app.integrations.adapters.gmail.handler import <PERSON><PERSON><PERSON><PERSON><PERSON>
from app.integrations.base.credentials_resolver import ICredentials
from app.integrations.base.email_adapter import BaseEmailAdapter
from app.integrations.schemas import EmailMessage, EmailThread
from app.integrations.types import IntegrationSource


class GmailAdapter(BaseEmailAdapter):
    """Gmail adapter that delegates to GmailHandler for business logic."""

    def __init__(self, credentials: ICredentials):
        super().__init__(credentials)
        self._handler = <PERSON>mail<PERSON>andler(credentials)

    @property
    def source(self) -> IntegrationSource:
        return IntegrationSource.GMAIL

    async def get_user_info(self) -> dict[str, Any]:
        return await self._handler.get_user_info()

    async def list_messages(
        self,
        query: str = "",
        max_results: int = 100,
        page_token: str | None = None,
        include_spam_trash: bool = False,
    ) -> list[EmailMessage]:
        return await self._handler.list_messages(
            query=query,
            max_results=max_results,
            page_token=page_token,
            include_spam_trash=include_spam_trash,
        )

    async def get_message(self, message_id: str) -> EmailMessage:
        return await self._handler.get_message(message_id)

    async def list_threads(
        self,
        query: str = "",
        max_results: int = 100,
        page_token: str | None = None,
        include_spam_trash: bool = False,
    ) -> list[EmailThread]:
        return await self._handler.list_threads(
            query=query,
            max_results=max_results,
            page_token=page_token,
            include_spam_trash=include_spam_trash,
        )

    async def get_thread(self, thread_id: str) -> EmailThread:
        return await self._handler.get_thread(thread_id)

    async def search_messages(
        self,
        sender: str | None = None,
        recipient: str | None = None,
        subject: str | None = None,
        has_attachment: bool | None = None,
        is_unread: bool | None = None,
        in_folder: str | None = None,
        after_date: datetime | None = None,
        before_date: datetime | None = None,
        query_text: str | None = None,
        max_results: int = 100,
    ) -> list[EmailMessage]:
        return await self._handler.search_messages(
            sender=sender,
            recipient=recipient,
            subject=subject,
            has_attachment=has_attachment,
            is_unread=is_unread,
            in_folder=in_folder,
            after_date=after_date,
            before_date=before_date,
            query_text=query_text,
            max_results=max_results,
        )

    async def modify_message(
        self,
        message_id: str,
        add_labels: list[str] | None = None,
        remove_labels: list[str] | None = None,
    ) -> dict[str, Any]:
        return await self._handler.modify_message(
            message_id=message_id,
            add_labels=add_labels,
            remove_labels=remove_labels,
        )

    async def list_labels(self) -> list[dict[str, Any]]:
        return await self._handler.list_labels()

    async def search_emails_by_context(
        self,
        company_name: str | None = None,
        client_name: str | None = None,
        domain: str | None = None,
        subject_keywords: str | None = None,
        date_range_days: int | None = 30,
        max_results: int = 20,
    ) -> list[EmailMessage]:
        """Search emails by business context with intelligent query building."""
        from datetime import datetime, timedelta

        # Build search parameters
        sender: str | None = None
        subject: str | None = None
        after_date: datetime | None = None
        query_text: str | None = None

        if domain:
            sender = f"*@{domain}"

        if subject_keywords:
            subject = subject_keywords

        if date_range_days:
            after_date = datetime.now() - timedelta(days=date_range_days)

        # Build query text for company/client names
        query_parts = []
        if company_name:
            query_parts.append(f'"{company_name}"')
        if client_name:
            query_parts.append(f'"{client_name}"')

        if query_parts:
            query_text = " ".join(query_parts)

        return await self.search_messages(
            sender=sender,
            subject=subject,
            after_date=after_date,
            query_text=query_text,
            max_results=max_results,
        )

    async def list_threads_with_filters(
        self,
        query: str | None = None,
        company_filter: str | None = None,
        client_filter: str | None = None,
        unread_only: bool = False,
        max_results: int = 50,
        include_spam_trash: bool = False,
    ) -> list[EmailThread]:
        """List threads with business logic filters."""
        # Build Gmail query
        query_parts = []

        if query:
            query_parts.append(query)
        if company_filter:
            query_parts.append(f"({company_filter})")
        if client_filter:
            query_parts.append(f"({client_filter})")
        if unread_only:
            query_parts.append("is:unread")

        gmail_query = " ".join(query_parts) if query_parts else ""

        return await self.list_threads(
            query=gmail_query,
            max_results=max_results,
            include_spam_trash=include_spam_trash,
        )

    async def list_filtered_labels(
        self, name_filter: str | None = None
    ) -> list[dict[str, Any]]:
        """List labels with optional name filtering."""
        labels = await self.list_labels()

        if name_filter:
            filter_lower = name_filter.lower()
            labels = [
                label
                for label in labels
                if filter_lower in label.get("name", "").lower()
            ]

        return labels

    async def get_thread_with_limit(
        self, thread_id: str, max_messages: int | None = None
    ) -> EmailThread:
        """Get thread with optional message limit."""
        thread = await self.get_thread(thread_id)

        if max_messages and len(thread.messages) > max_messages:
            # Create a new thread with limited messages
            limited_thread = EmailThread(
                id=thread.id,
                snippet=thread.snippet,
                messages=thread.messages[:max_messages],
                labels=thread.labels,
                history_id=thread.history_id,
            )
            return limited_thread

        return thread
