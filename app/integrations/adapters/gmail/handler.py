from datetime import datetime
from typing import Any

from app.common.helpers.logger import get_logger
from app.integrations.adapters.gmail.client import GmailClient
from app.integrations.adapters.gmail.utils import (
    convert_gmail_message_to_email_message,
    convert_gmail_thread_to_email_thread,
    create_gmail_query,
)
from app.integrations.adapters.google.refresh_token_client_mixin import (
    GoogleRefreshTokenClientMixin,
)
from app.integrations.base.credentials_resolver import ICredentials
from app.integrations.schemas import EmailMessage, EmailThread
from app.integrations.types import IntegrationSource

logger = get_logger()


class GmailHandler(GoogleRefreshTokenClientMixin[GmailClient]):
    """Gmail business logic handler with token refresh capabilities."""

    def __init__(self, credentials: ICredentials):
        self.init_google_client(credentials, IntegrationSource.GMAIL)

    @GoogleRefreshTokenClientMixin.handle_expired_session
    async def get_user_info(self) -> dict[str, Any]:
        return await self.google_client.get_user_info()

    @GoogleRefreshTokenClientMixin.handle_expired_session
    async def list_messages(
        self,
        query: str = "",
        max_results: int = 100,
        page_token: str | None = None,
        include_spam_trash: bool = False,
    ) -> list[EmailMessage]:
        response = await self.google_client.list_messages(
            query=query,
            max_results=max_results,
            page_token=page_token,
            include_spam_trash=include_spam_trash,
        )

        messages = []
        for msg_ref in response.get("messages", []):
            # Get full message details
            message_data = await self.google_client.get_message(msg_ref["id"])
            message = convert_gmail_message_to_email_message(message_data)
            messages.append(message)

        return messages

    @GoogleRefreshTokenClientMixin.handle_expired_session
    async def get_message(self, message_id: str) -> EmailMessage:
        message_data = await self.google_client.get_message(message_id)
        return convert_gmail_message_to_email_message(message_data)

    @GoogleRefreshTokenClientMixin.handle_expired_session
    async def list_threads(
        self,
        query: str = "",
        max_results: int = 100,
        page_token: str | None = None,
        include_spam_trash: bool = False,
    ) -> list[EmailThread]:
        response = await self.google_client.list_threads(
            query=query,
            max_results=max_results,
            page_token=page_token,
            include_spam_trash=include_spam_trash,
        )

        threads = []
        for thread_ref in response.get("threads", []):
            # Get full thread details
            thread_data = await self.google_client.get_thread(thread_ref["id"])
            thread = convert_gmail_thread_to_email_thread(thread_data)
            threads.append(thread)

        return threads

    @GoogleRefreshTokenClientMixin.handle_expired_session
    async def get_thread(self, thread_id: str) -> EmailThread:
        thread_data = await self.google_client.get_thread(thread_id)
        return convert_gmail_thread_to_email_thread(thread_data)

    @GoogleRefreshTokenClientMixin.handle_expired_session
    async def search_messages(
        self,
        sender: str | None = None,
        recipient: str | None = None,
        subject: str | None = None,
        has_attachment: bool | None = None,
        is_unread: bool | None = None,
        in_folder: str | None = None,
        after_date: datetime | None = None,
        before_date: datetime | None = None,
        query_text: str | None = None,
        max_results: int = 100,
    ) -> list[EmailMessage]:
        query = create_gmail_query(
            sender=sender,
            recipient=recipient,
            subject=subject,
            has_attachment=has_attachment,
            is_unread=is_unread,
            in_folder=in_folder,
            after_date=after_date,
            before_date=before_date,
            query_text=query_text,
        )

        return await self.list_messages(query=query, max_results=max_results)

    @GoogleRefreshTokenClientMixin.handle_expired_session
    async def modify_message(
        self,
        message_id: str,
        add_labels: list[str] | None = None,
        remove_labels: list[str] | None = None,
    ) -> dict[str, Any]:
        return await self.google_client.modify_message(
            message_id=message_id,
            add_label_ids=add_labels,
            remove_label_ids=remove_labels,
        )

    @GoogleRefreshTokenClientMixin.handle_expired_session
    async def list_labels(self) -> list[dict[str, Any]]:
        response = await self.google_client.list_labels()
        return response.get("labels", [])
