from datetime import datetime
from typing import Any

from app.integrations.base.credentials_resolver import ICredentialsResolver
from app.integrations.base.email_adapter import BaseEmailAdapter
from app.integrations.base.email_backend import BaseEmailBackend
from app.integrations.context import IntegrationContext
from app.integrations.schemas import EmailMessage, EmailThread
from app.integrations.types import IntegrationSource


class EmailBackend(BaseEmailBackend):
    """Email backend implementation that wraps email adapters."""

    def __init__(
        self,
        context: IntegrationContext,
        adapter_class: type[BaseEmailAdapter],
        source: IntegrationSource,
    ):
        super().__init__(context, adapter_class, source)

    async def _get_adapter(self) -> BaseEmailAdapter:
        credentials_resolver: ICredentialsResolver = self.context.credentials_resolver
        credentials = await credentials_resolver.get_credentials(self.source)
        return self.adapter_class(credentials)

    async def get_user_info(self) -> dict[str, Any]:
        """Get user profile information."""
        adapter = await self._get_adapter()
        return await adapter.get_user_info()

    async def list_messages(
        self,
        query: str = "",
        max_results: int = 100,
        page_token: str | None = None,
        include_spam_trash: bool = False,
    ) -> list[EmailMessage]:
        """List messages matching the query."""
        adapter = await self._get_adapter()
        return await adapter.list_messages(
            query=query,
            max_results=max_results,
            page_token=page_token,
            include_spam_trash=include_spam_trash,
        )

    async def get_message(self, message_id: str) -> EmailMessage:
        """Get a specific message by ID."""
        adapter = await self._get_adapter()
        return await adapter.get_message(message_id)

    async def list_threads(
        self,
        query: str = "",
        max_results: int = 100,
        page_token: str | None = None,
        include_spam_trash: bool = False,
    ) -> list[EmailThread]:
        """List threads matching the query."""
        adapter = await self._get_adapter()
        return await adapter.list_threads(
            query=query,
            max_results=max_results,
            page_token=page_token,
            include_spam_trash=include_spam_trash,
        )

    async def get_thread(self, thread_id: str) -> EmailThread:
        """Get a specific thread by ID."""
        adapter = await self._get_adapter()
        return await adapter.get_thread(thread_id)

    async def search_messages(
        self,
        sender: str | None = None,
        recipient: str | None = None,
        subject: str | None = None,
        has_attachment: bool | None = None,
        is_unread: bool | None = None,
        in_folder: str | None = None,
        after_date: datetime | None = None,
        before_date: datetime | None = None,
        query_text: str | None = None,
        max_results: int = 100,
    ) -> list[EmailMessage]:
        """Search messages with various criteria."""
        adapter = await self._get_adapter()
        return await adapter.search_messages(
            sender=sender,
            recipient=recipient,
            subject=subject,
            has_attachment=has_attachment,
            is_unread=is_unread,
            in_folder=in_folder,
            after_date=after_date,
            before_date=before_date,
            query_text=query_text,
            max_results=max_results,
        )

    async def modify_message(
        self,
        message_id: str,
        add_labels: list[str] | None = None,
        remove_labels: list[str] | None = None,
    ) -> dict[str, Any]:
        """Modify labels on a message."""
        adapter = await self._get_adapter()
        return await adapter.modify_message(
            message_id=message_id,
            add_labels=add_labels,
            remove_labels=remove_labels,
        )

    async def list_labels(self) -> list[dict[str, Any]]:
        """List all labels."""
        adapter = await self._get_adapter()
        return await adapter.list_labels()
