from typing import Any

from google.auth.credentials import Credentials
from google.oauth2.credentials import Credentials as OAuth2Credentials

from app.common.helpers.logger import get_logger

logger = get_logger()


class GoogleClientError(Exception):
    pass


class BaseGoogleClient:
    """Base class for Google API clients."""

    def __init__(self, credentials: dict[str, Any]):
        self._credentials_dict = credentials
        self._credentials = self._create_credentials(credentials)

    def _create_credentials(self, credentials: dict[str, Any]) -> Credentials:
        """Create Google OAuth2 credentials from dictionary."""
        try:
            scopes_str = credentials.get("scope", "")
            return OAuth2Credentials(
                token=credentials.get("access_token"),
                refresh_token=credentials.get("refresh_token"),
                token_uri=credentials.get(
                    "token_uri", "https://oauth2.googleapis.com/token"
                ),
                client_id=credentials.get("client_id"),
                client_secret=credentials.get("client_secret"),
                scopes=scopes_str.split(" ") if scopes_str else [],
            )
        except Exception as e:
            logger.error(f"Failed to create Google credentials: {e}")
            raise GoogleClientError(f"Failed to create Google credentials: {e}")

    @property
    def credentials(self) -> Credentials:
        """Get the Google credentials."""
        return self._credentials

    @property
    def credentials_dict(self) -> dict[str, Any]:
        """Get the raw credentials dictionary."""
        return self._credentials_dict
