from abc import ABC, abstractmethod
from typing import TYPE_CHECKING

from app.common.helpers.logger import get_logger
from app.integrations.base.credentials_resolver import ICredentials
from app.workspace.exceptions import (
    IntegrationDisconnectedError,
    IntegrationTokenNotFoundError,
    IntegrationTokenRefreshError,
)

if TYPE_CHECKING:
    from app.workspace.models import IntegrationUser
    from app.workspace.schemas import OrgEnvironment
    from app.workspace.services.base.oauth_connection import BaseOAuthConnection

logger = get_logger()


class BaseOAuthCredentials(ICredentials, ABC):
    """Base class for OAuth credentials with shared refresh logic."""

    def __init__(
        self,
        integration_user: "IntegrationUser",
        connection_service: "BaseOAuthConnection",
        environment: "OrgEnvironment",
    ):
        self._integration_user = integration_user
        self._connection_service = connection_service
        self._environment = environment
        self._integration_user_id = integration_user.id
        self._user_id = integration_user.user_id

    @abstractmethod
    def get_integration_name(self) -> str:
        pass

    async def refresh_token(self) -> "BaseOAuthCredentials":
        try:
            logger.debug(
                f"Attempting to refresh {self.get_integration_name()} token for user {self._user_id}"
            )

            token_response = await self._connection_service.refresh_access_token(
                integration_user_id=self._integration_user_id,
                environment=self._environment,
            )

            # Re-fetch updated integration_user from database to get all refreshed fields
            # (refresh_token, expires_at, last_refreshed_at, updated_at, etc.)
            updated_integration_user = (
                await self._connection_service.integration_user_repo.get_by_id(
                    self._integration_user_id
                )
            )
            if updated_integration_user:
                self._integration_user = updated_integration_user
                logger.debug(
                    "Local integration_user object updated with refreshed database fields"
                )

            logger.info(
                f"Successfully refreshed {self.get_integration_name()} token for user {self._user_id}, expires at {token_response.expires_at}"
            )
        except IntegrationTokenNotFoundError as e:
            logger.exception(
                f"Invalid refresh token for {self.get_integration_name()} user {self._user_id}: {e}"
            )
            raise IntegrationDisconnectedError(
                f"{self.get_integration_name()} refresh token invalid, re-authentication required"
            )
        except (ConnectionError, TimeoutError) as e:
            logger.warning(
                f"Network error during {self.get_integration_name()} token refresh for user {self._user_id}: {e}"
            )
            raise IntegrationTokenRefreshError(
                f"Temporary {self.get_integration_name()} refresh failure: {e}"
            )
        except Exception as e:
            logger.exception(
                f"Unexpected error during {self.get_integration_name()} token refresh for user {self._user_id}: {e}"
            )
            raise

        return self
