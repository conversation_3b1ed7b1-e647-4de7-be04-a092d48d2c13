import functools
from abc import ABC, abstractmethod
from collections.abc import Awaitable, Callable
from typing import Any, TypeVar

from googleapiclient.http import HttpError

from app.common.helpers.logger import get_logger
from app.integrations.base.credentials_resolver import ICredentials
from app.workspace.exceptions import (
    IntegrationDisconnectedError,
    IntegrationTokenExpiredError,
    IntegrationTokenNotFoundError,
    IntegrationTokenRefreshError,
)

logger = get_logger()

T = TypeVar("T")


class BaseOAuthRefreshTokenMixin(ABC):
    """Base mixin for OAuth integrations with automatic token refresh capability."""

    credentials: ICredentials

    @abstractmethod
    def get_integration_specific_auth_exceptions(self) -> tuple[type, ...]:
        pass

    @abstractmethod
    def get_client_cache_attribute_name(self) -> str:
        pass

    @abstractmethod
    async def refresh_integration_token(self):
        pass

    @abstractmethod
    def reinitialize_client(self):
        pass

    def _is_authentication_error(self, exception: Exception) -> bool:
        # 1. Check HTTP status codes (universal across all REST APIs)
        if hasattr(exception, "response"):
            status_code = getattr(exception.response, "status_code", None)
            if status_code in [401, 403]:  # Unauthorized/Forbidden
                return True

        if isinstance(exception, HttpError):
            return exception.resp.status in [401, 403]

        # 2. Check integration-specific authentication exceptions
        auth_exceptions = self.get_integration_specific_auth_exceptions()
        current: BaseException | None = exception
        while current:
            if isinstance(current, auth_exceptions):
                return True
            current = current.__cause__

        return False

    @staticmethod
    def handle_expired_session(
        method: Callable[..., Awaitable[T]],
    ) -> Callable[..., Awaitable[T]]:
        @functools.wraps(method)
        async def wrapper(self, *args: Any, **kwargs: Any) -> T:
            try:
                return await method(self, *args, **kwargs)
            except Exception as e:
                # Check the main exception and its cause chain for authentication errors
                current_exception: BaseException | None = e
                while current_exception is not None:
                    if isinstance(
                        current_exception, Exception
                    ) and self._is_authentication_error(current_exception):
                        logger.warning(
                            f"Authentication error during {method.__name__}: {current_exception}. Refreshing token..."
                        )
                        try:
                            await self.refresh_integration_token()
                            return await method(self, *args, **kwargs)
                        except IntegrationTokenNotFoundError:
                            logger.exception(
                                f"Invalid refresh token during {method.__name__}, disconnecting integration"
                            )
                            raise IntegrationDisconnectedError(
                                "Refresh token invalid, re-authentication required"
                            )
                        except (ConnectionError, TimeoutError) as refresh_error:
                            logger.warning(
                                f"Network error during token refresh in {method.__name__}: {refresh_error}"
                            )
                            raise IntegrationTokenRefreshError(
                                f"Temporary refresh failure: {refresh_error}"
                            )
                        except Exception as refresh_error:
                            logger.exception(
                                f"Token refresh failed permanently during {method.__name__}: {refresh_error}"
                            )
                            raise IntegrationTokenExpiredError(
                                "Unable to refresh expired token"
                            )

                    current_exception = current_exception.__cause__

                raise

        return wrapper
