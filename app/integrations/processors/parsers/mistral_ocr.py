import base64
import json

from mistralai import Mistral
from mistralai.extra import response_format_from_pydantic_model
from mistralai.models import DocumentURLChunk, ImageURLChunk
from pydantic import BaseModel

from app.common.helpers.logger import get_logger

logger = get_logger()


class Image(BaseModel):
    """Schema for image descriptions extracted by Mistral OCR."""

    description: str


class ImageData(BaseModel):
    """Data structure for images found within documents."""

    id: str
    image_annotation: str | None = None


class PageData(BaseModel):
    """Data structure for individual pages in OCR response."""

    markdown: str | None = None
    images: list[ImageData] | None = None


class MistralOCRResponse(BaseModel):
    """Complete response structure from Mistral OCR API."""

    pages: list[PageData]
    document_annotation: str | None = None


class MistralOCR:
    """
    Mistral OCR processor for extracting text and annotations from documents and images.

    Supports two types of annotation:
    - document_annotation_format: For analyzing entire standalone images
    - bbox_annotation_format: For analyzing embedded elements within documents
    """

    def __init__(self, api_key: str):
        self.client = Mistral(api_key=api_key)
        self.model = "mistral-ocr-latest"

        # Supported MIME types based on Mistral OCR documentation
        self.supported_image_types = {
            "image/png",
            "image/jpeg",
            "image/jpg",
            "image/avif",
        }
        self.supported_document_types = {
            "application/pdf",
            "application/vnd.openxmlformats-officedocument.presentationml.presentation",  # PPTX
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document",  # DOCX
        }

    async def parse(self, file_data: bytes, content_type: str) -> str:
        """
        Parse file using Mistral OCR with appropriate annotation strategy.

        Args:
            file_data: Raw file bytes
            content_type: MIME type of the file

        Returns:
            Extracted text content with annotations
        """
        self._validate_content_type(content_type)

        file_base64 = base64.b64encode(file_data).decode("utf-8")

        document = self._create_document_chunk(content_type, file_base64)

        try:
            response = await self._process_with_mistral(content_type, document)
            typed_response = MistralOCRResponse.model_validate(
                json.loads(response.model_dump_json())
            )

            return self._extract_content_with_annotations(typed_response)

        except Exception as e:
            logger.exception(f"Mistral OCR processing failed: {e}")
            raise

    def _validate_content_type(self, content_type: str) -> None:
        """Validate that the content type is supported."""
        if not (
            content_type in self.supported_image_types
            or content_type in self.supported_document_types
        ):
            raise ValueError(f"Unsupported content type: {content_type}")

    def _create_document_chunk(
        self, content_type: str, file_base64: str
    ) -> ImageURLChunk | DocumentURLChunk:
        """Create appropriate document chunk based on content type."""
        if content_type in self.supported_image_types:
            return ImageURLChunk(
                type="image_url",
                image_url=f"data:{content_type};base64,{file_base64}",
            )
        else:
            return DocumentURLChunk(
                type="document_url",
                document_url=f"data:application/pdf;base64,{file_base64}",
            )

    async def _process_with_mistral(
        self, content_type: str, document: ImageURLChunk | DocumentURLChunk
    ):
        """Process document with appropriate Mistral OCR annotation strategy."""
        # PPTX requires image_limit=0 to work
        is_pptx = (
            content_type
            == "application/vnd.openxmlformats-officedocument.presentationml.presentation"
        )

        if content_type in self.supported_image_types:
            # Standalone images: use document annotation to analyze the entire image
            return await self.client.ocr.process_async(
                model=self.model,
                document=document,
                include_image_base64=False,
                document_annotation_format=response_format_from_pydantic_model(Image),
            )
        else:
            # Documents: use bbox annotation for embedded elements
            return await self.client.ocr.process_async(
                model=self.model,
                document=document,
                include_image_base64=is_pptx,
                image_limit=0 if not is_pptx else None,
                bbox_annotation_format=response_format_from_pydantic_model(Image),
            )

    def _extract_content_with_annotations(self, response: MistralOCRResponse) -> str:
        """
        Extract markdown content and merge with image annotations.

        For documents: Replaces image references with descriptions
        For standalone images: Prioritizes document annotation over individual regions
        """
        page_contents = []

        # If document annotation exists (standalone images), prioritize it
        if response.document_annotation:
            # For standalone images, extract the document description directly
            try:
                annotation_data = json.loads(response.document_annotation)
                if (
                    isinstance(annotation_data, dict)
                    and "description" in annotation_data
                ):
                    return annotation_data["description"]
                else:
                    return response.document_annotation
            except json.JSONDecodeError:
                return response.document_annotation

        # For documents without document annotation, process pages normally
        for page in response.pages:
            markdown_content = page.markdown or ""

            # Process embedded images (mainly for documents)
            if page.images:
                markdown_content = self._replace_image_references(
                    markdown_content, page.images
                )

            if markdown_content.strip():
                page_contents.append(markdown_content)

        return "\n---\n".join(page_contents)

    def _replace_image_references(
        self, markdown_content: str, images: list[ImageData]
    ) -> str:
        """Replace image references in markdown with descriptions."""
        for img in images:
            img_reference = f"![{img.id}]({img.id})"
            if img_reference in markdown_content:
                description = self._extract_image_description(img.image_annotation)
                replacement = f"\n**Image Description:**\n{description}\n"
                markdown_content = markdown_content.replace(img_reference, replacement)
        return markdown_content

    def _extract_image_description(self, annotation: str | None) -> str:
        """Extract description from image annotation JSON."""
        if not annotation:
            return "Image description not available"

        try:
            annotation_data = json.loads(annotation)
            return annotation_data.get("description", "Image description not available")
        except json.JSONDecodeError:
            logger.exception(f"Failed to parse image annotation: {annotation}")
            return "Image description not available"
