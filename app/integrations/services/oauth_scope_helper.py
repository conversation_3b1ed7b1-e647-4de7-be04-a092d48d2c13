from app.common.helpers.logger import get_logger

logger = get_logger()


class GoogleOAuthScopes:
    """Common Google OAuth scopes for integrations."""

    GMAIL_READONLY = "https://www.googleapis.com/auth/gmail.readonly"
    GMAIL_COMPOSE = "https://www.googleapis.com/auth/gmail.compose"
    GMAIL_MODIFY = "https://www.googleapis.com/auth/gmail.modify"

    CALENDAR_READONLY = "https://www.googleapis.com/auth/calendar.readonly"
    CALENDAR_FULL = "https://www.googleapis.com/auth/calendar"

    PROFILE = "https://www.googleapis.com/auth/userinfo.profile"
    EMAIL = "https://www.googleapis.com/auth/userinfo.email"


class SalesforceOAuthScopes:
    """Common Salesforce OAuth scopes for integrations."""

    API = "api"
    REFRESH_TOKEN = "refresh_token"


class OAuthScopeHelper:
    """Helper class for managing Google OAuth scopes."""

    @staticmethod
    def get_gmail_scopes(full_access: bool = False) -> list[str]:
        """Get Gmail specific scopes."""
        if full_access:
            return [
                GoogleOAuthScopes.GMAIL_READONLY,
                GoogleOAuthScopes.GMAIL_COMPOSE,
                GoogleOAuthScopes.GMAIL_MODIFY,
                GoogleOAuthScopes.PROFILE,
                GoogleOAuthScopes.EMAIL,
            ]
        else:
            return [
                GoogleOAuthScopes.GMAIL_READONLY,
                GoogleOAuthScopes.PROFILE,
                GoogleOAuthScopes.EMAIL,
            ]

    @staticmethod
    def get_gmail_scopes_string(full_access: bool = False) -> str:
        """Get Gmail specific scopes as space-separated string."""
        return " ".join(OAuthScopeHelper.get_gmail_scopes(full_access))

    @staticmethod
    def get_calendar_scopes(readonly: bool = False) -> list[str]:
        """Get Calendar specific scopes."""
        if readonly:
            return [
                GoogleOAuthScopes.CALENDAR_READONLY,
                GoogleOAuthScopes.PROFILE,
                GoogleOAuthScopes.EMAIL,
            ]
        else:
            return [
                GoogleOAuthScopes.CALENDAR_FULL,
                GoogleOAuthScopes.PROFILE,
                GoogleOAuthScopes.EMAIL,
            ]

    @staticmethod
    def get_calendar_scopes_string(readonly: bool = False) -> str:
        """Get Calendar specific scopes as space-separated string."""
        return " ".join(OAuthScopeHelper.get_calendar_scopes(readonly))

    @staticmethod
    def get_combined_scopes(
        include_gmail: bool = True, include_calendar: bool = True
    ) -> list[str]:
        """Get combined scopes for both Gmail and Calendar."""
        scopes = [
            GoogleOAuthScopes.PROFILE,
            GoogleOAuthScopes.EMAIL,
        ]

        if include_gmail:
            scopes.extend(
                [
                    GoogleOAuthScopes.GMAIL_READONLY,
                    GoogleOAuthScopes.GMAIL_MODIFY,
                ]
            )

        if include_calendar:
            scopes.append(GoogleOAuthScopes.CALENDAR_FULL)

        return scopes

    @staticmethod
    def get_combined_scopes_string(
        include_gmail: bool = True, include_calendar: bool = True
    ) -> str:
        """Get combined scopes for both Gmail and Calendar as space-separated string."""
        return " ".join(
            OAuthScopeHelper.get_combined_scopes(include_gmail, include_calendar)
        )

    @staticmethod
    def get_salesforce_scopes(
        include_api: bool = True, include_refresh_token: bool = True
    ) -> list[str]:
        """Get Salesforce specific scopes."""
        scopes = []
        if include_api:
            scopes.append(SalesforceOAuthScopes.API)
        if include_refresh_token:
            scopes.append(SalesforceOAuthScopes.REFRESH_TOKEN)
        return scopes

    @staticmethod
    def get_salesforce_scopes_string(
        include_api: bool = True, include_refresh_token: bool = True
    ) -> str:
        """Get Salesforce specific scopes as space-separated string."""
        return " ".join(
            OAuthScopeHelper.get_salesforce_scopes(include_api, include_refresh_token)
        )
