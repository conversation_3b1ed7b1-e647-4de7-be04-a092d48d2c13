from datetime import datetime
from unittest.mock import Mock

from app.integrations.adapters.gmail.utils import (
    convert_gmail_message_to_email_message,
    convert_gmail_thread_to_email_thread,
    create_gmail_query,
    decode_base64url,
    extract_attachments,
    extract_message_body,
    extract_part_attachments,
    extract_part_body,
)
from app.integrations.schemas import EmailAttachment, EmailMessage, EmailThread


class TestDecodeBase64url:
    def test_decode_base64url_success(self):
        data = "SGVsbG8gV29ybGQ"  # "Hello World" in base64url
        result = decode_base64url(data)
        assert result == "Hello World"

    def test_decode_base64url_with_missing_padding(self):
        data = "SGVsbG8"  # "Hello" in base64url without padding
        result = decode_base64url(data)
        assert result == "Hello"

    def test_decode_base64url_url_safe_chars(self):
        data = "SGVsbG8tV29ybGRfVGVzdA"  # Contains - and _
        result = decode_base64url(data)
        assert "Hello" in result

    def test_decode_base64url_error(self, mocker):
        mock_logger = mocker.patch("app.integrations.adapters.gmail.utils.logger")

        invalid_data = "invalid_base64_data!!!"
        result = decode_base64url(invalid_data)

        assert result == invalid_data
        mock_logger.warning.assert_called_once()

    def test_decode_base64url_empty_string(self):
        result = decode_base64url("")
        assert result == ""


class TestCreateGmailQuery:
    def test_create_gmail_query_all_params(self):
        query = create_gmail_query(
            sender="<EMAIL>",
            recipient="<EMAIL>",
            subject="important",
            has_attachment=True,
            is_unread=True,
            in_folder="inbox",
            after_date=datetime(2022, 1, 1),
            before_date=datetime(2022, 12, 31),
            query_text="urgent",
        )

        expected_parts = [
            "from:<EMAIL>",
            "to:<EMAIL>",
            "subject:important",
            "has:attachment",
            "is:unread",
            "in:inbox",
            "after:2022/01/01",
            "before:2022/12/31",
            "urgent",
        ]

        for part in expected_parts:
            assert part in query

    def test_create_gmail_query_minimal_params(self):
        query = create_gmail_query()
        assert query == ""

    def test_create_gmail_query_single_param(self):
        query = create_gmail_query(sender="<EMAIL>")
        assert query == "from:<EMAIL>"

    def test_create_gmail_query_partial_params(self):
        query = create_gmail_query(
            sender="<EMAIL>", has_attachment=True, query_text="important"
        )

        assert "from:<EMAIL>" in query
        assert "has:attachment" in query
        assert "important" in query
        assert "to:" not in query
        assert "subject:" not in query


class TestExtractPartBody:
    def test_extract_part_body_text_plain(self, mocker):
        mock_decode = mocker.patch(
            "app.integrations.adapters.gmail.utils.decode_base64url",
            return_value="Hello World",
        )

        part = {"mimeType": "text/plain", "body": {"data": "SGVsbG8gV29ybGQ"}}

        result = extract_part_body(part)

        assert result == "Hello World"
        mock_decode.assert_called_once_with("SGVsbG8gV29ybGQ")

    def test_extract_part_body_no_data(self):
        part = {"mimeType": "text/plain", "body": {}}

        result = extract_part_body(part)
        assert result == ""

    def test_extract_part_body_wrong_mime_type(self):
        part = {"mimeType": "text/html", "body": {"data": "SGVsbG8gV29ybGQ"}}

        result = extract_part_body(part)
        assert result == ""

    def test_extract_part_body_with_nested_parts(self, mocker):
        mock_decode = mocker.patch(
            "app.integrations.adapters.gmail.utils.decode_base64url",
            return_value="Part content",
        )

        part = {
            "parts": [
                {"mimeType": "text/plain", "body": {"data": "part1"}},
                {"mimeType": "text/plain", "body": {"data": "part2"}},
            ]
        }

        result = extract_part_body(part)

        assert result == "Part contentPart content"
        assert mock_decode.call_count == 2


class TestExtractMessageBody:
    def test_extract_message_body_with_parts(self, mocker):
        mock_extract_part = mocker.patch(
            "app.integrations.adapters.gmail.utils.extract_part_body",
            side_effect=["Part 1", "Part 2"],
        )

        payload = {"parts": [{"part": 1}, {"part": 2}]}

        result = extract_message_body(payload)

        assert result == "Part 1Part 2"
        assert mock_extract_part.call_count == 2

    def test_extract_message_body_no_parts(self, mocker):
        mock_extract_part = mocker.patch(
            "app.integrations.adapters.gmail.utils.extract_part_body",
            return_value="Single part content",
        )

        payload = {"body": {"data": "content"}}

        result = extract_message_body(payload)

        assert result == "Single part content"
        mock_extract_part.assert_called_once_with(payload)


class TestExtractPartAttachments:
    def test_extract_part_attachments_with_attachment(self):
        part = {
            "filename": "document.pdf",
            "mimeType": "application/pdf",
            "body": {"attachmentId": "attach123", "size": 1024},
        }

        result = extract_part_attachments(part)

        assert len(result) == 1
        attachment = result[0]
        assert isinstance(attachment, EmailAttachment)
        assert attachment.id == "attach123"
        assert attachment.filename == "document.pdf"
        assert attachment.mime_type == "application/pdf"
        assert attachment.size == 1024

    def test_extract_part_attachments_no_attachment(self):
        part = {"mimeType": "text/plain", "body": {"data": "content"}}

        result = extract_part_attachments(part)
        assert result == []

    def test_extract_part_attachments_nested_parts(self):
        part = {
            "parts": [
                {
                    "filename": "doc1.pdf",
                    "body": {"attachmentId": "attach1", "size": 512},
                },
                {
                    "filename": "doc2.pdf",
                    "body": {"attachmentId": "attach2", "size": 1024},
                },
            ]
        }

        result = extract_part_attachments(part)

        assert len(result) == 2
        assert result[0].filename == "doc1.pdf"
        assert result[1].filename == "doc2.pdf"


class TestExtractAttachments:
    def test_extract_attachments_with_parts(self, mocker):
        mock_extract_part = mocker.patch(
            "app.integrations.adapters.gmail.utils.extract_part_attachments",
            side_effect=[
                [
                    EmailAttachment(
                        id="1",
                        filename="file1.pdf",
                        mime_type="application/pdf",
                        size=100,
                    )
                ],
                [
                    EmailAttachment(
                        id="2", filename="file2.jpg", mime_type="image/jpeg", size=200
                    )
                ],
            ],
        )

        payload = {"parts": [{"part": 1}, {"part": 2}]}

        result = extract_attachments(payload)

        assert len(result) == 2
        assert result[0].filename == "file1.pdf"
        assert result[1].filename == "file2.jpg"
        assert mock_extract_part.call_count == 2

    def test_extract_attachments_no_parts(self, mocker):
        mock_extract_part = mocker.patch(
            "app.integrations.adapters.gmail.utils.extract_part_attachments",
            return_value=[
                EmailAttachment(
                    id="1", filename="file.pdf", mime_type="application/pdf", size=100
                )
            ],
        )

        payload = {"body": {"data": "content"}}

        result = extract_attachments(payload)

        assert len(result) == 1
        assert result[0].filename == "file.pdf"
        mock_extract_part.assert_called_once_with(payload)


class TestConvertGmailMessageToEmailMessage:
    def test_convert_gmail_message_success(self, mocker):
        mock_extract_body = mocker.patch(
            "app.integrations.adapters.gmail.utils.extract_message_body",
            return_value="Message body",
        )
        mock_extract_attachments = mocker.patch(
            "app.integrations.adapters.gmail.utils.extract_attachments", return_value=[]
        )

        message_data = {
            "id": "msg123",
            "threadId": "thread456",
            "snippet": "Test snippet",
            "internalDate": "1640995200000",  # 2022-01-01 00:00:00
            "sizeEstimate": 1024,
            "labelIds": ["INBOX", "UNREAD"],
            "payload": {
                "headers": [
                    {"name": "Subject", "value": "Test Subject"},
                    {"name": "From", "value": "<EMAIL>"},
                    {"name": "To", "value": "<EMAIL>"},
                    {"name": "Cc", "value": "<EMAIL>"},
                    {"name": "Bcc", "value": "<EMAIL>"},
                ]
            },
        }

        result = convert_gmail_message_to_email_message(message_data)

        assert isinstance(result, EmailMessage)
        assert result.id == "msg123"
        assert result.thread_id == "thread456"
        assert result.subject == "Test Subject"
        assert result.sender == "<EMAIL>"
        assert result.recipients == "<EMAIL>"
        assert result.cc == "<EMAIL>"
        assert result.bcc == "<EMAIL>"
        assert result.date == datetime.fromtimestamp(1640995200)
        assert result.body == "Message body"
        assert result.snippet == "Test snippet"
        assert result.labels == ["INBOX", "UNREAD"]
        assert result.size_estimate == 1024
        assert result.raw_data == message_data

        mock_extract_body.assert_called_once()
        mock_extract_attachments.assert_called_once()

    def test_convert_gmail_message_minimal_data(self, mocker):
        mocker.patch(
            "app.integrations.adapters.gmail.utils.extract_message_body",
            return_value="",
        )
        mocker.patch(
            "app.integrations.adapters.gmail.utils.extract_attachments", return_value=[]
        )

        message_data = {
            "id": "msg123",
            "threadId": "thread456",
        }

        result = convert_gmail_message_to_email_message(message_data)

        assert result.id == "msg123"
        assert result.thread_id == "thread456"
        assert result.subject == ""
        assert result.sender == ""
        assert result.recipients == ""
        assert result.cc is None
        assert result.bcc is None
        assert result.date is None
        assert result.snippet == ""
        assert result.labels == []
        assert result.size_estimate == 0


class TestConvertGmailThreadToEmailThread:
    def test_convert_gmail_thread_success(self, mocker):
        mock_convert_message = mocker.patch(
            "app.integrations.adapters.gmail.utils.convert_gmail_message_to_email_message",
            return_value=EmailMessage(
                id="msg1",
                thread_id="thread123",
                subject="Test",
                sender="<EMAIL>",
                recipients="<EMAIL>",
                cc=None,
                bcc=None,
                date=None,
                body="",
                snippet="",
                labels=[],
                attachments=[],
                size_estimate=0,
                raw_data={},
            ),
        )

        thread_data = {
            "id": "thread123",
            "snippet": "Thread snippet",
            "labelIds": ["INBOX"],
            "historyId": "12345",
            "messages": [
                {"id": "msg1", "threadId": "thread123"},
                {"id": "msg2", "threadId": "thread123"},
            ],
        }

        result = convert_gmail_thread_to_email_thread(thread_data)

        assert isinstance(result, EmailThread)
        assert result.id == "thread123"
        assert result.snippet == "Thread snippet"
        assert result.labels == ["INBOX"]
        assert result.history_id == "12345"
        assert len(result.messages) == 2

        assert mock_convert_message.call_count == 2

    def test_convert_gmail_thread_minimal_data(self, mocker):
        mocker.patch(
            "app.integrations.adapters.gmail.utils.convert_gmail_message_to_email_message",
            return_value=Mock(),
        )

        thread_data = {
            "id": "thread123",
        }

        result = convert_gmail_thread_to_email_thread(thread_data)

        assert result.id == "thread123"
        assert result.snippet == ""
        assert result.labels == []
        assert result.history_id is None
        assert len(result.messages) == 0
