import pytest
from googleapiclient.errors import HttpError

from app.integrations.adapters.gmail.client import GmailClient


@pytest.fixture
def mock_credentials():
    return {
        "access_token": "mock_access_token",
        "refresh_token": "mock_refresh_token",
        "client_id": "mock_client_id",
        "client_secret": "mock_client_secret",
        "token_uri": "https://oauth2.googleapis.com/token",
        "scope": "https://www.googleapis.com/auth/gmail.readonly",
    }


@pytest.fixture
def mock_http_error():
    def _create_error(status_code, message):
        mock_resp = type(
            "MockResponse",
            (),
            {
                "status": status_code,
                "reason": "Test Error",
                "version": 11,
            },
        )()

        error_content = {"error": {"code": status_code, "message": message}}

        return HttpError(resp=mock_resp, content=str(error_content).encode())

    return _create_error


@pytest.fixture
def gmail_client(mocker, mock_credentials):
    mock_service = mocker.MagicMock()

    # Mock users() chain
    mock_users = mocker.MagicMock()
    mock_service.users.return_value = mock_users

    # Mock users().getProfile() chain
    mock_profile = mocker.MagicMock()
    mock_users.getProfile.return_value = mock_profile

    # Mock users().messages() chain
    mock_messages = mocker.MagicMock()
    mock_users.messages.return_value = mock_messages

    # Mock users().threads() chain
    mock_threads = mocker.MagicMock()
    mock_users.threads.return_value = mock_threads

    # Mock users().labels() chain
    mock_labels = mocker.MagicMock()
    mock_users.labels.return_value = mock_labels

    mocker.patch(
        "app.integrations.adapters.gmail.client.build",
        return_value=mock_service,
    )
    mocker.patch("app.integrations.base.google_client.OAuth2Credentials")

    client = GmailClient(credentials=mock_credentials)

    # Reset mocks after initialization
    mock_service.reset_mock()
    mock_users.reset_mock()
    mock_profile.reset_mock()
    mock_messages.reset_mock()
    mock_threads.reset_mock()
    mock_labels.reset_mock()

    return client


def test_create_client_success(mocker, mock_credentials):
    mock_oauth_creds = mocker.MagicMock()
    mock_service = mocker.MagicMock()

    oauth_mock = mocker.patch(
        "app.integrations.base.google_client.OAuth2Credentials",
        return_value=mock_oauth_creds,
    )
    build_mock = mocker.patch(
        "app.integrations.adapters.gmail.client.build",
        return_value=mock_service,
    )

    client = GmailClient(credentials=mock_credentials)

    # Trigger service property to initialize
    service = client.service

    oauth_mock.assert_called_once()
    build_mock.assert_called_once_with("gmail", "v1", credentials=mock_oauth_creds)
    assert service == mock_service


@pytest.mark.anyio
async def test_get_user_info_success(gmail_client):
    expected_result = {
        "emailAddress": "<EMAIL>",
        "messagesTotal": 1000,
        "threadsTotal": 500,
        "historyId": "12345",
    }

    mock_execute = (
        gmail_client.service.users.return_value.getProfile.return_value.execute
    )
    mock_execute.return_value = expected_result

    result = await gmail_client.get_user_info()

    assert result == expected_result
    gmail_client.service.users.assert_called_once()
    gmail_client.service.users.return_value.getProfile.assert_called_once_with(
        userId="me"
    )
    mock_execute.assert_called_once()


@pytest.mark.anyio
async def test_get_user_info_error(gmail_client, mock_http_error):
    mock_execute = (
        gmail_client.service.users.return_value.getProfile.return_value.execute
    )
    mock_execute.side_effect = mock_http_error(401, "Unauthorized")

    with pytest.raises(HttpError):
        await gmail_client.get_user_info()


@pytest.mark.anyio
async def test_list_messages_success(gmail_client):
    expected_result = {
        "messages": [
            {"id": "msg1", "threadId": "thread1"},
            {"id": "msg2", "threadId": "thread2"},
        ],
        "nextPageToken": "next_token",
        "resultSizeEstimate": 2,
    }

    mock_list = gmail_client.service.users.return_value.messages.return_value.list
    mock_execute = mock_list.return_value.execute
    mock_execute.return_value = expected_result

    result = await gmail_client.list_messages(
        query="from:<EMAIL>",
        max_results=50,
        page_token="page_token",  # noqa: S106
        include_spam_trash=True,
    )

    assert result == expected_result
    mock_list.assert_called_once_with(
        userId="me",
        q="from:<EMAIL>",
        maxResults=50,
        pageToken="page_token",
        includeSpamTrash=True,
    )
    mock_execute.assert_called_once()


@pytest.mark.anyio
async def test_list_messages_minimal_params(gmail_client):
    expected_result = {"messages": [], "resultSizeEstimate": 0}

    mock_list = gmail_client.service.users.return_value.messages.return_value.list
    mock_execute = mock_list.return_value.execute
    mock_execute.return_value = expected_result

    result = await gmail_client.list_messages()

    assert result == expected_result
    mock_list.assert_called_once_with(
        userId="me",
        q="",
        maxResults=100,
        pageToken=None,
        includeSpamTrash=False,
    )
    mock_execute.assert_called_once()


@pytest.mark.anyio
async def test_list_messages_error(gmail_client, mock_http_error):
    mock_execute = gmail_client.service.users.return_value.messages.return_value.list.return_value.execute
    mock_execute.side_effect = mock_http_error(400, "Bad Request")

    with pytest.raises(HttpError):
        await gmail_client.list_messages()


@pytest.mark.anyio
async def test_get_message_success(gmail_client):
    message_id = "msg123"
    expected_result = {
        "id": message_id,
        "threadId": "thread123",
        "snippet": "Test message snippet",
        "payload": {
            "headers": [
                {"name": "Subject", "value": "Test Subject"},
                {"name": "From", "value": "<EMAIL>"},
            ]
        },
        "sizeEstimate": 1024,
    }

    mock_get = gmail_client.service.users.return_value.messages.return_value.get
    mock_execute = mock_get.return_value.execute
    mock_execute.return_value = expected_result

    result = await gmail_client.get_message(message_id, message_format="metadata")

    assert result == expected_result
    mock_get.assert_called_once_with(userId="me", id=message_id, format="metadata")
    mock_execute.assert_called_once()


@pytest.mark.anyio
async def test_get_message_default_format(gmail_client):
    message_id = "msg123"
    expected_result = {"id": message_id}

    mock_get = gmail_client.service.users.return_value.messages.return_value.get
    mock_execute = mock_get.return_value.execute
    mock_execute.return_value = expected_result

    result = await gmail_client.get_message(message_id)

    assert result == expected_result
    mock_get.assert_called_once_with(userId="me", id=message_id, format="full")
    mock_execute.assert_called_once()


@pytest.mark.anyio
async def test_get_message_error(gmail_client, mock_http_error):
    message_id = "invalid_msg"
    mock_execute = gmail_client.service.users.return_value.messages.return_value.get.return_value.execute
    mock_execute.side_effect = mock_http_error(404, "Not Found")

    with pytest.raises(HttpError):
        await gmail_client.get_message(message_id)


@pytest.mark.anyio
async def test_list_labels_success(gmail_client):
    expected_result = {
        "labels": [
            {"id": "INBOX", "name": "INBOX", "type": "system"},
            {"id": "SENT", "name": "SENT", "type": "system"},
            {"id": "custom_label", "name": "Custom Label", "type": "user"},
        ]
    }

    mock_list = gmail_client.service.users.return_value.labels.return_value.list
    mock_execute = mock_list.return_value.execute
    mock_execute.return_value = expected_result

    result = await gmail_client.list_labels()

    assert result == expected_result
    mock_list.assert_called_once_with(userId="me")
    mock_execute.assert_called_once()


@pytest.mark.anyio
async def test_list_labels_error(gmail_client, mock_http_error):
    mock_execute = gmail_client.service.users.return_value.labels.return_value.list.return_value.execute
    mock_execute.side_effect = mock_http_error(403, "Forbidden")

    with pytest.raises(HttpError):
        await gmail_client.list_labels()


@pytest.mark.anyio
async def test_modify_message_success(gmail_client):
    message_id = "msg123"
    expected_result = {
        "id": message_id,
        "labelIds": ["INBOX", "UNREAD", "custom_label"],
    }

    mock_modify = gmail_client.service.users.return_value.messages.return_value.modify
    mock_execute = mock_modify.return_value.execute
    mock_execute.return_value = expected_result

    result = await gmail_client.modify_message(
        message_id,
        add_label_ids=["custom_label"],
        remove_label_ids=["UNREAD"],
    )

    assert result == expected_result
    mock_modify.assert_called_once_with(
        userId="me",
        id=message_id,
        body={"addLabelIds": ["custom_label"], "removeLabelIds": ["UNREAD"]},
    )
    mock_execute.assert_called_once()


@pytest.mark.anyio
async def test_modify_message_add_only(gmail_client):
    message_id = "msg123"
    expected_result = {"id": message_id}

    mock_modify = gmail_client.service.users.return_value.messages.return_value.modify
    mock_execute = mock_modify.return_value.execute
    mock_execute.return_value = expected_result

    result = await gmail_client.modify_message(message_id, add_label_ids=["STARRED"])

    assert result == expected_result
    mock_modify.assert_called_once_with(
        userId="me", id=message_id, body={"addLabelIds": ["STARRED"]}
    )


@pytest.mark.anyio
async def test_modify_message_remove_only(gmail_client):
    message_id = "msg123"
    expected_result = {"id": message_id}

    mock_modify = gmail_client.service.users.return_value.messages.return_value.modify
    mock_execute = mock_modify.return_value.execute
    mock_execute.return_value = expected_result

    result = await gmail_client.modify_message(message_id, remove_label_ids=["UNREAD"])

    assert result == expected_result
    mock_modify.assert_called_once_with(
        userId="me", id=message_id, body={"removeLabelIds": ["UNREAD"]}
    )


@pytest.mark.anyio
async def test_modify_message_error(gmail_client, mock_http_error):
    message_id = "invalid_msg"
    mock_execute = gmail_client.service.users.return_value.messages.return_value.modify.return_value.execute
    mock_execute.side_effect = mock_http_error(404, "Not Found")

    with pytest.raises(HttpError):
        await gmail_client.modify_message(message_id, add_label_ids=["STARRED"])


@pytest.mark.anyio
async def test_list_threads_success(gmail_client):
    expected_result = {
        "threads": [
            {"id": "thread1", "snippet": "Thread 1 snippet"},
            {"id": "thread2", "snippet": "Thread 2 snippet"},
        ],
        "nextPageToken": "next_token",
        "resultSizeEstimate": 2,
    }

    mock_list = gmail_client.service.users.return_value.threads.return_value.list
    mock_execute = mock_list.return_value.execute
    mock_execute.return_value = expected_result

    result = await gmail_client.list_threads(
        query="subject:important",
        max_results=25,
        page_token="token",  # noqa: S106
        include_spam_trash=False,
    )

    assert result == expected_result
    mock_list.assert_called_once_with(
        userId="me",
        q="subject:important",
        maxResults=25,
        pageToken="token",
        includeSpamTrash=False,
    )
    mock_execute.assert_called_once()


@pytest.mark.anyio
async def test_get_thread_success(gmail_client):
    thread_id = "thread123"
    expected_result = {
        "id": thread_id,
        "snippet": "Thread snippet",
        "messages": [
            {"id": "msg1", "threadId": thread_id},
            {"id": "msg2", "threadId": thread_id},
        ],
    }

    mock_get = gmail_client.service.users.return_value.threads.return_value.get
    mock_execute = mock_get.return_value.execute
    mock_execute.return_value = expected_result

    result = await gmail_client.get_thread(thread_id, thread_format="metadata")

    assert result == expected_result
    mock_get.assert_called_once_with(userId="me", id=thread_id, format="metadata")
    mock_execute.assert_called_once()


@pytest.mark.anyio
async def test_get_attachment_success(gmail_client):
    message_id = "msg123"
    attachment_id = "attach123"
    expected_result = {
        "size": 1024,
        "data": "base64_encoded_data",
    }

    mock_get = gmail_client.service.users.return_value.messages.return_value.attachments.return_value.get
    mock_execute = mock_get.return_value.execute
    mock_execute.return_value = expected_result

    result = await gmail_client.get_attachment(message_id, attachment_id)

    assert result == expected_result
    mock_get.assert_called_once_with(
        userId="me", messageId=message_id, id=attachment_id
    )
    mock_execute.assert_called_once()


@pytest.mark.anyio
async def test_get_attachment_error(gmail_client, mock_http_error):
    message_id = "msg123"
    attachment_id = "invalid_attach"
    mock_execute = gmail_client.service.users.return_value.messages.return_value.attachments.return_value.get.return_value.execute
    mock_execute.side_effect = mock_http_error(404, "Not Found")

    with pytest.raises(HttpError):
        await gmail_client.get_attachment(message_id, attachment_id)
