from datetime import datetime
from unittest.mock import AsyncMock, Mock, patch

import pytest

from app.integrations.adapters.gmail.adapter import GmailAdapter
from app.integrations.base.credentials_resolver import ICredentials
from app.integrations.schemas import EmailMessage, EmailThread
from app.integrations.types import IntegrationSource


@pytest.fixture
def mock_credentials():
    credentials = Mock(spec=ICredentials)
    credentials.secrets = {
        "access_token": "test_access_token",
        "refresh_token": "test_refresh_token",
        "client_id": "test_client_id",
        "client_secret": "test_client_secret",
        "token_uri": "https://oauth2.googleapis.com/token",
        "scope": "https://www.googleapis.com/auth/gmail.readonly",
        "scopes": ["https://www.googleapis.com/auth/gmail.readonly"],
    }
    return credentials


@pytest.fixture
def mock_handler():
    handler = AsyncMock()

    # Mock get_user_info
    handler.get_user_info.return_value = {
        "emailAddress": "<EMAIL>",
        "messagesTotal": 1000,
        "threadsTotal": 500,
        "historyId": "12345",
    }

    # Mock list_messages
    handler.list_messages.return_value = [
        EmailMessage(
            id="msg1",
            thread_id="thread1",
            subject="Test Message 1",
            sender="<EMAIL>",
            recipients="<EMAIL>",
            cc=None,
            bcc=None,
            date=datetime(2022, 1, 1, 10, 0, 0),
            body="Message body 1",
            snippet="Test message snippet 1",
            labels=["INBOX"],
            attachments=[],
            size_estimate=1024,
            raw_data={},
        ),
        EmailMessage(
            id="msg2",
            thread_id="thread2",
            subject="Test Message 2",
            sender="<EMAIL>",
            recipients="<EMAIL>",
            cc="<EMAIL>",
            bcc=None,
            date=datetime(2022, 1, 2, 15, 30, 0),
            body="Message body 2",
            snippet="Test message snippet 2",
            labels=["INBOX", "IMPORTANT"],
            attachments=[],
            size_estimate=2048,
            raw_data={},
        ),
    ]

    # Mock get_message
    handler.get_message.return_value = EmailMessage(
        id="msg123",
        thread_id="thread123",
        subject="Single Test Message",
        sender="<EMAIL>",
        recipients="<EMAIL>",
        cc=None,
        bcc=None,
        date=datetime(2022, 1, 1, 12, 0, 0),
        body="Single message body",
        snippet="Single message snippet",
        labels=["INBOX", "UNREAD"],
        attachments=[],
        size_estimate=512,
        raw_data={},
    )

    # Mock list_threads
    handler.list_threads.return_value = [
        EmailThread(
            id="thread1",
            snippet="Thread 1 snippet",
            messages=[
                EmailMessage(
                    id="msg1",
                    thread_id="thread1",
                    subject="Thread Message",
                    sender="<EMAIL>",
                    recipients="<EMAIL>",
                    cc=None,
                    bcc=None,
                    date=datetime(2022, 1, 1, 10, 0, 0),
                    body="Thread message body",
                    snippet="Thread message snippet",
                    labels=["INBOX"],
                    attachments=[],
                    size_estimate=1024,
                    raw_data={},
                )
            ],
            labels=["INBOX"],
            history_id="12345",
        )
    ]

    # Mock get_thread
    handler.get_thread.return_value = EmailThread(
        id="thread456",
        snippet="Single thread snippet",
        messages=[
            EmailMessage(
                id="msg_in_thread",
                thread_id="thread456",
                subject="Message in Thread",
                sender="<EMAIL>",
                recipients="<EMAIL>",
                cc=None,
                bcc=None,
                date=datetime(2022, 1, 1, 14, 0, 0),
                body="Message in thread body",
                snippet="Message in thread snippet",
                labels=["INBOX"],
                attachments=[],
                size_estimate=256,
                raw_data={},
            )
        ],
        labels=["INBOX", "IMPORTANT"],
        history_id="67890",
    )

    # Mock search_messages
    handler.search_messages.return_value = [
        EmailMessage(
            id="search_result",
            thread_id="search_thread",
            subject="Search Result Message",
            sender="<EMAIL>",
            recipients="<EMAIL>",
            cc=None,
            bcc=None,
            date=datetime(2022, 1, 3, 9, 0, 0),
            body="Search result body",
            snippet="Search result snippet",
            labels=["INBOX", "STARRED"],
            attachments=[],
            size_estimate=768,
            raw_data={},
        )
    ]

    # Mock modify_message
    handler.modify_message.return_value = {
        "id": "msg123",
        "labelIds": ["INBOX", "STARRED"],
    }

    # Mock list_labels
    handler.list_labels.return_value = [
        {"id": "INBOX", "name": "INBOX", "type": "system"},
        {"id": "SENT", "name": "SENT", "type": "system"},
        {"id": "STARRED", "name": "STARRED", "type": "system"},
    ]

    return handler


@pytest.fixture
def gmail_adapter(mock_credentials, mock_handler):
    with patch(
        "app.integrations.adapters.gmail.adapter.GmailHandler",
        return_value=mock_handler,
    ):
        return GmailAdapter(mock_credentials)


class TestGmailAdapter:
    def test_source_property(self, gmail_adapter):
        assert gmail_adapter.source == IntegrationSource.GMAIL

    def test_init(self, mock_credentials):
        with patch(
            "app.integrations.adapters.gmail.adapter.GmailHandler"
        ) as mock_handler_class:
            adapter = GmailAdapter(mock_credentials)

            assert adapter.credentials == mock_credentials
            mock_handler_class.assert_called_once_with(mock_credentials)

    @pytest.mark.anyio
    async def test_get_user_info(self, gmail_adapter, mock_handler):
        result = await gmail_adapter.get_user_info()

        assert result["emailAddress"] == "<EMAIL>"
        assert result["messagesTotal"] == 1000
        assert result["threadsTotal"] == 500
        assert result["historyId"] == "12345"

        mock_handler.get_user_info.assert_called_once()

    @pytest.mark.anyio
    async def test_list_messages_success(self, gmail_adapter, mock_handler):
        result = await gmail_adapter.list_messages(
            query="from:<EMAIL>",
            max_results=50,
            page_token="page_token",  # noqa: S106
            include_spam_trash=True,
        )

        assert len(result) == 2
        assert isinstance(result[0], EmailMessage)
        assert result[0].id == "msg1"
        assert result[0].subject == "Test Message 1"
        assert result[0].sender == "<EMAIL>"
        assert result[1].id == "msg2"
        assert result[1].cc == "<EMAIL>"

        mock_handler.list_messages.assert_called_once_with(
            query="from:<EMAIL>",
            max_results=50,
            page_token="page_token",  # noqa: S106
            include_spam_trash=True,
        )

    @pytest.mark.anyio
    async def test_list_messages_minimal_params(self, gmail_adapter, mock_handler):
        result = await gmail_adapter.list_messages()

        assert len(result) == 2
        mock_handler.list_messages.assert_called_once_with(
            query="",
            max_results=100,
            page_token=None,
            include_spam_trash=False,
        )

    @pytest.mark.anyio
    async def test_get_message(self, gmail_adapter, mock_handler):
        message_id = "msg123"
        result = await gmail_adapter.get_message(message_id)

        assert isinstance(result, EmailMessage)
        assert result.id == "msg123"
        assert result.subject == "Single Test Message"
        assert result.sender == "<EMAIL>"
        assert result.snippet == "Single message snippet"
        assert "UNREAD" in result.labels

        mock_handler.get_message.assert_called_once_with(message_id)

    @pytest.mark.anyio
    async def test_list_threads_success(self, gmail_adapter, mock_handler):
        result = await gmail_adapter.list_threads(
            query="subject:important",
            max_results=25,
            page_token="token",  # noqa: S106
            include_spam_trash=False,
        )

        assert len(result) == 1
        assert isinstance(result[0], EmailThread)
        assert result[0].id == "thread1"
        assert result[0].snippet == "Thread 1 snippet"
        assert len(result[0].messages) == 1
        assert result[0].messages[0].subject == "Thread Message"

        mock_handler.list_threads.assert_called_once_with(
            query="subject:important",
            max_results=25,
            page_token="token",  # noqa: S106
            include_spam_trash=False,
        )

    @pytest.mark.anyio
    async def test_list_threads_minimal_params(self, gmail_adapter, mock_handler):
        result = await gmail_adapter.list_threads()

        assert len(result) == 1
        mock_handler.list_threads.assert_called_once_with(
            query="",
            max_results=100,
            page_token=None,
            include_spam_trash=False,
        )

    @pytest.mark.anyio
    async def test_get_thread(self, gmail_adapter, mock_handler):
        thread_id = "thread456"
        result = await gmail_adapter.get_thread(thread_id)

        assert isinstance(result, EmailThread)
        assert result.id == "thread456"
        assert result.snippet == "Single thread snippet"
        assert len(result.messages) == 1
        assert result.messages[0].subject == "Message in Thread"
        assert "IMPORTANT" in result.labels

        mock_handler.get_thread.assert_called_once_with(thread_id)

    @pytest.mark.anyio
    async def test_search_messages_full_params(self, gmail_adapter, mock_handler):
        result = await gmail_adapter.search_messages(
            sender="<EMAIL>",
            recipient="<EMAIL>",
            subject="important",
            has_attachment=True,
            is_unread=True,
            in_folder="inbox",
            after_date=datetime(2022, 1, 1),
            before_date=datetime(2022, 12, 31),
            query_text="urgent",
            max_results=50,
        )

        assert len(result) == 1
        assert isinstance(result[0], EmailMessage)
        assert result[0].id == "search_result"
        assert result[0].subject == "Search Result Message"
        assert result[0].sender == "<EMAIL>"

        mock_handler.search_messages.assert_called_once_with(
            sender="<EMAIL>",
            recipient="<EMAIL>",
            subject="important",
            has_attachment=True,
            is_unread=True,
            in_folder="inbox",
            after_date=datetime(2022, 1, 1),
            before_date=datetime(2022, 12, 31),
            query_text="urgent",
            max_results=50,
        )

    @pytest.mark.anyio
    async def test_search_messages_minimal_params(self, gmail_adapter, mock_handler):
        result = await gmail_adapter.search_messages()

        assert len(result) == 1
        mock_handler.search_messages.assert_called_once_with(
            sender=None,
            recipient=None,
            subject=None,
            has_attachment=None,
            is_unread=None,
            in_folder=None,
            after_date=None,
            before_date=None,
            query_text=None,
            max_results=100,
        )

    @pytest.mark.anyio
    async def test_search_messages_partial_params(self, gmail_adapter, mock_handler):
        result = await gmail_adapter.search_messages(
            sender="<EMAIL>",
            has_attachment=True,
            max_results=25,
        )

        assert len(result) == 1
        mock_handler.search_messages.assert_called_once_with(
            sender="<EMAIL>",
            recipient=None,
            subject=None,
            has_attachment=True,
            is_unread=None,
            in_folder=None,
            after_date=None,
            before_date=None,
            query_text=None,
            max_results=25,
        )

    @pytest.mark.anyio
    async def test_modify_message_full_params(self, gmail_adapter, mock_handler):
        message_id = "msg123"
        add_labels = ["STARRED", "IMPORTANT"]
        remove_labels = ["UNREAD"]

        result = await gmail_adapter.modify_message(
            message_id,
            add_labels=add_labels,
            remove_labels=remove_labels,
        )

        assert result["id"] == "msg123"
        assert "STARRED" in result["labelIds"]

        mock_handler.modify_message.assert_called_once_with(
            message_id=message_id,
            add_labels=add_labels,
            remove_labels=remove_labels,
        )

    @pytest.mark.anyio
    async def test_modify_message_add_only(self, gmail_adapter, mock_handler):
        message_id = "msg123"
        add_labels = ["STARRED"]

        result = await gmail_adapter.modify_message(
            message_id,
            add_labels=add_labels,
        )

        assert result["id"] == "msg123"
        mock_handler.modify_message.assert_called_once_with(
            message_id=message_id,
            add_labels=add_labels,
            remove_labels=None,
        )

    @pytest.mark.anyio
    async def test_modify_message_remove_only(self, gmail_adapter, mock_handler):
        message_id = "msg123"
        remove_labels = ["UNREAD"]

        result = await gmail_adapter.modify_message(
            message_id,
            remove_labels=remove_labels,
        )

        assert result["id"] == "msg123"
        mock_handler.modify_message.assert_called_once_with(
            message_id=message_id,
            add_labels=None,
            remove_labels=remove_labels,
        )

    @pytest.mark.anyio
    async def test_modify_message_minimal_params(self, gmail_adapter, mock_handler):
        message_id = "msg123"

        result = await gmail_adapter.modify_message(message_id)

        assert result["id"] == "msg123"
        mock_handler.modify_message.assert_called_once_with(
            message_id=message_id,
            add_labels=None,
            remove_labels=None,
        )

    @pytest.mark.anyio
    async def test_list_labels(self, gmail_adapter, mock_handler):
        result = await gmail_adapter.list_labels()

        assert len(result) == 3
        assert result[0]["id"] == "INBOX"
        assert result[1]["id"] == "SENT"
        assert result[2]["id"] == "STARRED"

        # Check label types
        assert all(label.get("type") in ["system"] for label in result)

        mock_handler.list_labels.assert_called_once()

    @pytest.mark.anyio
    async def test_list_labels_empty(self, gmail_adapter, mock_handler):
        # Mock empty labels response
        mock_handler.list_labels.return_value = []

        result = await gmail_adapter.list_labels()

        assert len(result) == 0
        mock_handler.list_labels.assert_called_once()

    def test_inheritance(self, gmail_adapter):
        """Test that GmailAdapter properly inherits from BaseEmailAdapter."""
        from app.integrations.base.email_adapter import BaseEmailAdapter

        assert isinstance(gmail_adapter, BaseEmailAdapter)

    @pytest.mark.anyio
    async def test_error_propagation(self, gmail_adapter, mock_handler):
        """Test that errors from handler are properly propagated."""
        # Mock handler to raise an exception
        mock_handler.get_user_info.side_effect = Exception("Handler error")

        with pytest.raises(Exception, match="Handler error"):
            await gmail_adapter.get_user_info()

        mock_handler.get_user_info.assert_called_once()

    @pytest.mark.anyio
    async def test_all_methods_delegate_to_handler(self, gmail_adapter, mock_handler):
        """Test that all adapter methods properly delegate to handler."""
        # Test each method to ensure proper delegation
        await gmail_adapter.get_user_info()
        mock_handler.get_user_info.assert_called()

        await gmail_adapter.list_messages()
        mock_handler.list_messages.assert_called()

        await gmail_adapter.get_message("msg1")
        mock_handler.get_message.assert_called_with("msg1")

        await gmail_adapter.list_threads()
        mock_handler.list_threads.assert_called()

        await gmail_adapter.get_thread("thread1")
        mock_handler.get_thread.assert_called_with("thread1")

        await gmail_adapter.search_messages()
        mock_handler.search_messages.assert_called()

        await gmail_adapter.modify_message("msg1")
        mock_handler.modify_message.assert_called_with(
            message_id="msg1",
            add_labels=None,
            remove_labels=None,
        )

        await gmail_adapter.list_labels()
        mock_handler.list_labels.assert_called()

    @pytest.mark.anyio
    async def test_search_emails_by_context_full_params(
        self, gmail_adapter, mock_handler
    ):
        """Test the new search_emails_by_context method with all parameters."""
        # Mock the search_messages method to capture the parameters
        mock_handler.search_messages.return_value = [Mock(spec=EmailMessage)]

        result = await gmail_adapter.search_emails_by_context(
            company_name="ACME Corp",
            client_name="John Doe",
            domain="acme.com",
            subject_keywords="meeting",
            date_range_days=7,
            max_results=10,
        )

        assert len(result) == 1
        # Verify that search_messages was called with the right parameters
        mock_handler.search_messages.assert_called_once()
        call_args = mock_handler.search_messages.call_args

        # Check some of the key parameters
        assert call_args.kwargs["max_results"] == 10
        assert call_args.kwargs["sender"] == "*@acme.com"
        assert call_args.kwargs["subject"] == "meeting"
        assert '"ACME Corp" "John Doe"' in call_args.kwargs["query_text"]

    @pytest.mark.anyio
    async def test_list_threads_with_filters(self, gmail_adapter, mock_handler):
        """Test the new list_threads_with_filters method."""
        mock_handler.list_threads.return_value = [Mock(spec=EmailThread)]

        result = await gmail_adapter.list_threads_with_filters(
            query="test query",
            company_filter="ACME",
            client_filter="client1",
            unread_only=True,
            max_results=25,
            include_spam_trash=False,
        )

        assert len(result) == 1
        mock_handler.list_threads.assert_called_once()
        call_args = mock_handler.list_threads.call_args

        # The query should include all the filters
        query = call_args.kwargs["query"]
        assert "test query" in query
        assert "(ACME)" in query
        assert "(client1)" in query
        assert "is:unread" in query

    @pytest.mark.anyio
    async def test_list_filtered_labels(self, gmail_adapter, mock_handler):
        """Test the new list_filtered_labels method."""
        mock_handler.list_labels.return_value = [
            {"id": "INBOX", "name": "INBOX", "type": "system"},
            {"id": "WORK", "name": "Work", "type": "user"},
            {"id": "PERSONAL", "name": "Personal", "type": "user"},
        ]

        # Test with filter
        result = await gmail_adapter.list_filtered_labels(name_filter="work")
        assert len(result) == 1
        assert result[0]["name"] == "Work"

        # Test without filter
        result = await gmail_adapter.list_filtered_labels()
        assert len(result) == 3

    @pytest.mark.anyio
    async def test_get_thread_with_limit(self, gmail_adapter, mock_handler):
        """Test the new get_thread_with_limit method."""
        # Create a mock thread with multiple messages
        mock_messages = [Mock(spec=EmailMessage) for _ in range(5)]
        mock_thread = EmailThread(
            id="thread123",
            snippet="Test thread",
            messages=mock_messages,
            labels=["INBOX"],
            history_id="12345",
        )
        mock_handler.get_thread.return_value = mock_thread

        # Test with message limit
        result = await gmail_adapter.get_thread_with_limit("thread123", max_messages=3)
        assert len(result.messages) == 3
        assert result.id == "thread123"

        # Test without limit
        result = await gmail_adapter.get_thread_with_limit("thread123")
        assert len(result.messages) == 5
