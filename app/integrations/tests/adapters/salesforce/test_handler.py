# ruff: noqa: S106

import pytest

from app.integrations.adapters.salesforce.client import (
    SalesforceClientError,
)
from app.integrations.adapters.salesforce.handler import (
    SalesforceHandler,
    SalesforceObjectType,
)
from app.integrations.base.credentials_resolver import (
    ICredentials,
)
from app.integrations.schemas import CRMAccountAccessData, CRMMetrics


@pytest.fixture
def mock_credentials(mocker):
    mock_credentials = mocker.Mock(spec=ICredentials)
    mock_credentials.secrets = {
        "username": "<EMAIL>",
        "password": "password123",
        "security_token": "token123",
    }
    return mock_credentials


@pytest.fixture
def mock_oauth_credentials(mocker):
    mock_credentials = mocker.Mock(spec=ICredentials)
    mock_credentials.secrets = {
        "access_token": "mock_access_token",
        "instance_url": "https://test.salesforce.com",
    }
    return mock_credentials


@pytest.fixture
def sfdc_handler(mocker, mock_oauth_credentials):
    mock_client = mocker.MagicMock()
    mock_client.get_object = mocker.AsyncMock()
    mock_client.list_objects = mocker.AsyncMock()
    mock_client.query = mocker.AsyncMock()
    mock_client.get_opportunity_metrics = mocker.AsyncMock()
    mock_client.get_quota = mocker.AsyncMock()

    mocker.patch(
        "app.integrations.adapters.salesforce.refresh_token_client_mixin.SalesforceClient",
        return_value=mock_client,
    )
    handler = SalesforceHandler(credentials=mock_oauth_credentials)
    assert handler.salesforce_client is mock_client
    return handler


def test_init_with_oauth_credentials(mocker, mock_oauth_credentials):
    mock_client = mocker.MagicMock()
    client_mock = mocker.patch(
        "app.integrations.adapters.salesforce.refresh_token_client_mixin.SalesforceClient",
        return_value=mock_client,
    )
    handler = SalesforceHandler(credentials=mock_oauth_credentials)

    client_mock.assert_called_once_with(
        instance_url="https://test.salesforce.com", access_token="mock_access_token"
    )
    assert handler.salesforce_client is mock_client


def test_init_with_missing_instance_url(mocker):
    mock_credentials = mocker.Mock(spec=ICredentials)
    mock_credentials.secrets = {
        "access_token": "mock_access_token",
    }
    with pytest.raises(
        ValueError, match="Missing required 'access_token' or 'instance_url'"
    ):
        SalesforceHandler(credentials=mock_credentials)


def test_init_with_invalid_credentials(mocker):
    mock_credentials = mocker.Mock(spec=ICredentials)
    mock_credentials.secrets = {
        "username": "<EMAIL>",
    }
    with pytest.raises(
        ValueError, match="Missing required 'access_token' or 'instance_url'"
    ):
        SalesforceHandler(credentials=mock_credentials)


def test_client_creation_with_username_password(mock_credentials):
    # Username/password authentication is no longer supported, should raise ValueError
    with pytest.raises(
        ValueError, match="Missing required 'access_token' or 'instance_url'"
    ):
        SalesforceHandler(credentials=mock_credentials)


@pytest.mark.anyio
async def test_get_opportunity_success(sfdc_handler):
    expected_opportunity = {
        "Id": "006XXXXXXXXXXXX",
        "Name": "Test Opportunity",
        "Amount": 50000,
        "CloseDate": "2025-12-31",
        "StageName": "Prospecting",
    }
    sfdc_handler.salesforce_client.get_object.return_value = expected_opportunity
    result = await sfdc_handler.get_opportunity("006XXXXXXXXXXXX")
    assert result == expected_opportunity
    sfdc_handler.salesforce_client.get_object.assert_called_once_with(
        SalesforceObjectType.OPPORTUNITY.value, "006XXXXXXXXXXXX"
    )


@pytest.mark.anyio
async def test_get_opportunity_error(sfdc_handler):
    sfdc_handler.salesforce_client.get_object.side_effect = SalesforceClientError(
        "Record not found"
    )
    with pytest.raises(SalesforceClientError, match="Record not found"):
        await sfdc_handler.get_opportunity("006XXXXXXXXXXXX")


@pytest.mark.anyio
async def test_list_opportunities_by_account_success(sfdc_handler):
    expected_opportunities = [
        {
            "Id": "006XXXXXXXXXXXX",
            "Name": "Opportunity 1",
            "Amount": 10000,
        },
        {
            "Id": "006YYYYYYYYYYYY",
            "Name": "Opportunity 2",
            "Amount": 20000,
        },
    ]
    sfdc_handler.salesforce_client.list_objects.return_value = expected_opportunities
    result = await sfdc_handler.list_opportunities_by_account(
        account_id="001XXXXXXXXXXXX",
        fields="ALL",
        limit=10,
        offset=0,
    )
    assert result == expected_opportunities
    sfdc_handler.salesforce_client.list_objects.assert_called_once_with(
        object_type=SalesforceObjectType.OPPORTUNITY.value,
        fields="ALL",
        where_clause="AccountId = '001XXXXXXXXXXXX'",
        order_by=None,
        limit=10,
        offset=0,
    )


@pytest.mark.anyio
async def test_list_opportunities_by_account_empty(sfdc_handler):
    result = await sfdc_handler.list_opportunities_by_account(account_id="")
    assert result == []
    sfdc_handler.salesforce_client.list_objects.assert_not_called()


@pytest.mark.anyio
async def test_list_opportunities_by_account_error(sfdc_handler):
    sfdc_handler.salesforce_client.list_objects.side_effect = SalesforceClientError(
        "Failed to execute query"
    )
    with pytest.raises(SalesforceClientError, match="Failed to execute query"):
        await sfdc_handler.list_opportunities_by_account("001XXXXXXXXXXXX")


@pytest.mark.anyio
async def test_search_opportunities_success(sfdc_handler):
    expected_opportunities = [
        {
            "Id": "006XXXXXXXXXXXX",
            "Name": "Big Deal",
            "StageName": "Prospecting",
            "Amount": 100000,
        }
    ]
    sfdc_handler.salesforce_client.list_objects.return_value = expected_opportunities
    search_criteria = {"Name": "Big", "StageName": "Prospecting"}
    result = await sfdc_handler.search_opportunities(
        search_criteria, limit=10, offset=0
    )
    assert result == expected_opportunities
    sfdc_handler.salesforce_client.list_objects.assert_called_once_with(
        object_type=SalesforceObjectType.OPPORTUNITY.value,
        fields=None,
        where_clause="Name LIKE '%Big%' AND StageName LIKE '%Prospecting%'",
        order_by=None,
        limit=10,
        offset=0,
    )


@pytest.mark.anyio
async def test_search_opportunities_empty_criteria(sfdc_handler):
    result = await sfdc_handler.search_opportunities({})
    assert result == []
    sfdc_handler.salesforce_client.list_objects.assert_not_called()


@pytest.mark.anyio
async def test_get_account_success(sfdc_handler):
    expected_account = {
        "Id": "001XXXXXXXXXXXX",
        "Name": "Test Account",
        "Industry": "Technology",
    }
    sfdc_handler.salesforce_client.get_object.return_value = expected_account
    result = await sfdc_handler.get_account("001XXXXXXXXXXXX")
    assert result == expected_account
    sfdc_handler.salesforce_client.get_object.assert_called_once_with(
        SalesforceObjectType.ACCOUNT.value, "001XXXXXXXXXXXX"
    )


@pytest.mark.anyio
async def test_get_account_error(sfdc_handler):
    sfdc_handler.salesforce_client.get_object.side_effect = SalesforceClientError(
        "Record not found"
    )
    with pytest.raises(SalesforceClientError, match="Record not found"):
        await sfdc_handler.get_account("001XXXXXXXXXXXX")


@pytest.mark.anyio
async def test_search_accounts_success(sfdc_handler):
    expected_accounts = [
        {
            "Id": "001XXXXXXXXXXXX",
            "Name": "Tech Corp",
            "Industry": "Technology",
        }
    ]
    sfdc_handler.salesforce_client.list_objects.return_value = expected_accounts
    search_criteria = {"Name": "Tech", "Industry": "Technology"}
    result = await sfdc_handler.search_accounts(search_criteria, limit=10, offset=0)
    assert result == expected_accounts
    sfdc_handler.salesforce_client.list_objects.assert_called_once_with(
        object_type=SalesforceObjectType.ACCOUNT.value,
        fields=None,
        where_clause="Name LIKE '%Tech%' AND Industry LIKE '%Technology%'",
        order_by=None,
        limit=10,
        offset=0,
    )


@pytest.mark.anyio
async def test_search_accounts_empty_criteria(sfdc_handler):
    result = await sfdc_handler.search_accounts({})
    assert result == []
    sfdc_handler.salesforce_client.list_objects.assert_not_called()


@pytest.mark.anyio
async def test_resolve_account_access(mocker, sfdc_handler):
    mock_access_data = [
        CRMAccountAccessData(
            account_id="001XXXXXXXXXXXX",
            account_name="Account 1",
            access_type="owner",
            access_role=None,
        )
    ]
    mock_resolver = mocker.MagicMock()
    mock_resolver.get_user_account_access = mocker.AsyncMock(
        return_value=mock_access_data
    )
    mocker.patch(
        "app.integrations.adapters.salesforce.handler.SalesforceAccountAccessResolver",
        return_value=mock_resolver,
    )
    result = await sfdc_handler.resolve_account_access(
        salesforce_user_id="005XXXXXXXXXXXX"
    )
    from app.integrations.adapters.salesforce.handler import (
        SalesforceAccountAccessResolver,
    )

    SalesforceAccountAccessResolver.assert_called_once_with(
        client=sfdc_handler.salesforce_client
    )
    mock_resolver.get_user_account_access.assert_called_once_with("005XXXXXXXXXXXX")
    assert result == mock_access_data


@pytest.mark.anyio
async def test_get_contact_success(sfdc_handler):
    expected_contact = {
        "Id": "003XXXXXXXXXXXX",
        "FirstName": "John",
        "LastName": "Doe",
        "Email": "<EMAIL>",
    }
    sfdc_handler.salesforce_client.get_object.return_value = expected_contact
    result = await sfdc_handler.get_contact("003XXXXXXXXXXXX")
    assert result == expected_contact
    sfdc_handler.salesforce_client.get_object.assert_called_once_with(
        SalesforceObjectType.CONTACT.value, "003XXXXXXXXXXXX"
    )


@pytest.mark.anyio
async def test_get_contact_error(sfdc_handler):
    sfdc_handler.salesforce_client.get_object.side_effect = SalesforceClientError(
        "Record not found"
    )
    with pytest.raises(SalesforceClientError, match="Record not found"):
        await sfdc_handler.get_contact("003XXXXXXXXXXXX")


@pytest.mark.anyio
async def test_list_contacts_by_account_success(sfdc_handler):
    expected_contacts = [
        {
            "Id": "003XXXXXXXXXXXX",
            "FirstName": "John",
            "LastName": "Doe",
        },
        {
            "Id": "003YYYYYYYYYYYY",
            "FirstName": "Jane",
            "LastName": "Smith",
        },
    ]
    sfdc_handler.salesforce_client.list_objects.return_value = expected_contacts
    result = await sfdc_handler.list_contacts_by_account(
        account_id="001XXXXXXXXXXXX",
        fields="ALL",
        limit=10,
        offset=0,
    )
    assert result == expected_contacts
    sfdc_handler.salesforce_client.list_objects.assert_called_once_with(
        object_type=SalesforceObjectType.CONTACT.value,
        fields="ALL",
        where_clause="AccountId = '001XXXXXXXXXXXX'",
        order_by=None,
        limit=10,
        offset=0,
    )


@pytest.mark.anyio
async def test_list_contacts_by_account_empty(sfdc_handler):
    result = await sfdc_handler.list_contacts_by_account(account_id="")
    assert result == []
    sfdc_handler.salesforce_client.list_objects.assert_not_called()


@pytest.mark.anyio
async def test_search_contacts_success(sfdc_handler):
    expected_contacts = [
        {
            "Id": "003XXXXXXXXXXXX",
            "FirstName": "John",
            "LastName": "Doe",
            "Email": "<EMAIL>",
        }
    ]
    sfdc_handler.salesforce_client.list_objects.return_value = expected_contacts
    search_criteria = {"Email": "<EMAIL>", "FirstName": "John"}
    result = await sfdc_handler.search_contacts(search_criteria, limit=10, offset=0)
    assert result == expected_contacts
    sfdc_handler.salesforce_client.list_objects.assert_called_once_with(
        object_type=SalesforceObjectType.CONTACT.value,
        fields=None,
        where_clause="Email = '<EMAIL>' AND FirstName LIKE '%John%'",
        order_by=None,
        limit=10,
        offset=0,
    )


@pytest.mark.anyio
async def test_search_contacts_empty_criteria(sfdc_handler):
    result = await sfdc_handler.search_contacts({})
    assert result == []
    sfdc_handler.salesforce_client.list_objects.assert_not_called()


@pytest.mark.anyio
async def test_get_task_success(sfdc_handler):
    expected_task = {
        "Id": "00TXXXXXXXXXXXX",
        "Subject": "Call Client",
        "Status": "Not Started",
        "Priority": "Normal",
    }
    sfdc_handler.salesforce_client.get_object.return_value = expected_task
    result = await sfdc_handler.get_task("00TXXXXXXXXXXXX")
    assert result == expected_task
    sfdc_handler.salesforce_client.get_object.assert_called_once_with(
        SalesforceObjectType.TASK.value, "00TXXXXXXXXXXXX"
    )


@pytest.mark.anyio
async def test_get_event_success(sfdc_handler):
    expected_event = {
        "Id": "00UXXXXXXXXXXXX",
        "Subject": "Client Meeting",
        "StartDateTime": "2024-01-15T10:00:00Z",
        "EndDateTime": "2024-01-15T11:00:00Z",
    }
    sfdc_handler.salesforce_client.get_object.return_value = expected_event
    result = await sfdc_handler.get_event("00UXXXXXXXXXXXX")
    assert result == expected_event
    sfdc_handler.salesforce_client.get_object.assert_called_once_with(
        SalesforceObjectType.EVENT.value, "00UXXXXXXXXXXXX"
    )


@pytest.mark.anyio
async def test_get_metrics_success(sfdc_handler):
    mock_metrics = {
        "closed_won": 50000.0,
        "pipeline": 50000.0,
        "forecast": 17500.0,
    }

    sfdc_handler.salesforce_client.get_opportunity_metrics.return_value = mock_metrics
    sfdc_handler.salesforce_client.get_quota.return_value = 100000.0

    result = await sfdc_handler.get_metrics("005XXXXXXXXXXXX")

    sfdc_handler.salesforce_client.get_opportunity_metrics.assert_called_once_with(
        "005XXXXXXXXXXXX", None
    )

    sfdc_handler.salesforce_client.get_quota.assert_called_once_with(
        "005XXXXXXXXXXXX", None
    )

    assert result == CRMMetrics(
        quota=100000,
        closed_won=50000,
        pipeline=50000,
        forecast=17500,
    )


@pytest.mark.anyio
async def test_get_metrics_with_null_values(sfdc_handler):
    mock_metrics = {
        "closed_won": 0.0,
        "pipeline": 0.0,
        "forecast": 0.0,
    }

    sfdc_handler.salesforce_client.get_opportunity_metrics.return_value = mock_metrics
    sfdc_handler.salesforce_client.get_quota.return_value = 0.0

    result = await sfdc_handler.get_metrics("005XXXXXXXXXXXX")

    assert result == CRMMetrics(
        quota=0,
        closed_won=0,
        pipeline=0,
        forecast=0,
    )


@pytest.mark.anyio
async def test_get_metrics_error_handling(sfdc_handler):
    sfdc_handler.salesforce_client.get_opportunity_metrics.side_effect = (
        SalesforceClientError("API Error")
    )

    result = await sfdc_handler.get_metrics("005XXXXXXXXXXXX")

    assert result == CRMMetrics(
        quota=0,
        closed_won=0,
        pipeline=0,
        forecast=0,
    )


@pytest.mark.anyio
async def test_get_metrics_case_insensitive_stage_names(sfdc_handler):
    mock_metrics = {
        "closed_won": 80000.0,
        "pipeline": 0.0,
        "forecast": 0.0,
    }

    sfdc_handler.salesforce_client.get_opportunity_metrics.return_value = mock_metrics
    sfdc_handler.salesforce_client.get_quota.return_value = 50000.0

    result = await sfdc_handler.get_metrics("005XXXXXXXXXXXX")

    assert result == CRMMetrics(
        quota=50000,
        closed_won=80000,
        pipeline=0,
        forecast=0,
    )


@pytest.mark.anyio
async def test_get_metrics_with_field_mapping(sfdc_handler):
    mock_metrics = {
        "closed_won": 75000.0,
        "pipeline": 120000.0,
        "forecast": 45000.0,
        "quota": 200000.0,
    }

    sfdc_handler.salesforce_client.get_opportunity_metrics.return_value = mock_metrics
    sfdc_handler.salesforce_client.get_quota.return_value = 200000.0

    field_mapping = {
        "opportunity_amount_field": "Amount",
        "opportunity_stage_field": "StageName",
        "opportunity_owner_field": "OwnerId",
        "opportunity_probability_field": "Probability",
        "closed_won_stage_pattern": "Closed Won",
        "closed_lost_stage_pattern": "Closed Lost",
        "forecast_probability_multiplier": 1.0,
        "use_probability_field": True,
        "quota_table": "ForecastingQuota",
        "quota_amount_field": "QuotaAmount",
        "quota_owner_field": "QuotaOwnerId",
    }

    result = await sfdc_handler.get_metrics("005XXXXXXXXXXXX", field_mapping)

    sfdc_handler.salesforce_client.get_opportunity_metrics.assert_called_once_with(
        "005XXXXXXXXXXXX", field_mapping
    )

    assert result == CRMMetrics(
        quota=200000,
        closed_won=75000,
        pipeline=120000,
        forecast=45000,
    )
