from datetime import datetime
from unittest.mock import AsyncMock

import pytest

from app.integrations.base.calendar_backend import BaseCalendarBackend
from app.integrations.base.crm_backend import BaseCRMBackend
from app.integrations.base.file_backend import BaseFileBackend
from app.integrations.base.messaging_backend import BaseMessagingBackend
from app.integrations.handles import (
    BaseHandle,
    CalendarHandle,
    CRMHandle,
    FileHandle,
    MessagingHandle,
)
from app.integrations.schemas import CRMMetrics
from app.integrations.types import IntegrationSource


@pytest.fixture
def mock_crm_backend(mocker):
    backend = mocker.Mock(spec=BaseCRMBackend)

    async def mock_get_opportunity(*_args, **_kwargs):
        return {"id": "123", "name": "Test Opp"}

    async def mock_update_opportunity(*_args, **_kwargs):
        return {"id": "123", "name": "Updated Opp"}

    async def mock_list_opportunities_by_account(*_args, **_kwargs):
        return [{"id": "123"}, {"id": "456"}]

    async def mock_search_opportunities(*_args, **_kwargs):
        return [{"id": "opp123", "Name": "Big Deal"}]

    async def mock_get_account(*_args, **_kwargs):
        return {"id": "acc123", "name": "Test Account"}

    async def mock_update_account(*_args, **_kwargs):
        return {"id": "acc123", "name": "Updated Account"}

    async def mock_search_accounts(*_args, **_kwargs):
        return [{"id": "acc123", "Name": "Tech Corp"}]

    async def mock_get_contact(*_args, **_kwargs):
        return {"id": "con123", "name": "Test Contact"}

    async def mock_create_contact(*_args, **_kwargs):
        return {"id": "con123", "FirstName": "John", "LastName": "Doe"}

    async def mock_update_contact(*_args, **_kwargs):
        return {"id": "con123", "FirstName": "Jane"}

    async def mock_list_contacts_by_account(*_args, **_kwargs):
        return [{"id": "con123"}, {"id": "con456"}]

    async def mock_search_contacts(*_args, **_kwargs):
        return [{"id": "con123", "Email": "<EMAIL>"}]

    async def mock_get_task(*_args, **_kwargs):
        return {"id": "tsk123", "subject": "Test Task"}

    async def mock_create_task(*_args, **_kwargs):
        return {"id": "tsk123", "Subject": "New Task", "Status": "Not Started"}

    async def mock_update_task(*_args, **_kwargs):
        return {"id": "tsk123", "Status": "Completed"}

    async def mock_list_tasks_by_contact(*_args, **_kwargs):
        return [{"id": "tsk123"}, {"id": "tsk456"}]

    async def mock_list_tasks_by_account(*_args, **_kwargs):
        return [{"id": "tsk123"}, {"id": "tsk456"}]

    async def mock_get_event(*_args, **_kwargs):
        return {"id": "evt123", "subject": "Test Event"}

    async def mock_create_event(*_args, **_kwargs):
        return {
            "id": "evt123",
            "Subject": "New Event",
            "StartDateTime": "2024-01-15T10:00:00Z",
        }

    async def mock_update_event(*_args, **_kwargs):
        return {"id": "evt123", "Subject": "Updated Event"}

    async def mock_list_events_by_contact(*_args, **_kwargs):
        return [{"id": "evt123"}, {"id": "evt456"}]

    async def mock_list_events_by_account(*_args, **_kwargs):
        return [{"id": "evt123"}, {"id": "evt456"}]

    async def mock_list_account_access(*_args, **_kwargs):
        return [{"account_id": "acc123", "access_type": "read"}]

    async def mock_bulk_sync_account_access(*_args, **_kwargs):
        return {"status": "started", "job_id": "job123"}

    async def mock_store_account_summary(*_args, **_kwargs):
        return

    async def mock_get_metrics(*_args, **_kwargs):
        return CRMMetrics(
            quota=1000000,
            closed_won=250000,
            pipeline=500000,
            forecast=300000,
        )

    # Assign the async mock functions
    backend.get_opportunity = mock_get_opportunity
    backend.update_opportunity = mock_update_opportunity
    backend.list_opportunities_by_account = mock_list_opportunities_by_account
    backend.search_opportunities = mock_search_opportunities
    backend.get_account = mock_get_account
    backend.update_account = mock_update_account
    backend.search_accounts = mock_search_accounts
    backend.get_contact = mock_get_contact
    backend.create_contact = mock_create_contact
    backend.update_contact = mock_update_contact
    backend.list_contacts_by_account = mock_list_contacts_by_account
    backend.search_contacts = mock_search_contacts
    backend.get_task = mock_get_task
    backend.create_task = mock_create_task
    backend.update_task = mock_update_task
    backend.list_tasks_by_contact = mock_list_tasks_by_contact
    backend.list_tasks_by_account = mock_list_tasks_by_account
    backend.get_event = mock_get_event
    backend.create_event = mock_create_event
    backend.update_event = mock_update_event
    backend.list_events_by_contact = mock_list_events_by_contact
    backend.list_events_by_account = mock_list_events_by_account
    backend.list_account_access = mock_list_account_access
    backend.bulk_sync_account_access = mock_bulk_sync_account_access
    backend.store_account_summary = mock_store_account_summary
    backend.get_metrics = mock_get_metrics

    return backend


@pytest.fixture
def mock_messaging_backend(mocker):
    return mocker.Mock(spec=BaseMessagingBackend)


@pytest.fixture
def mock_file_backend(mocker):
    backend = mocker.Mock(spec=BaseFileBackend)
    backend.upload_file = AsyncMock()
    return backend


def test_base_handle_initialization(mock_crm_backend):
    mock_crm_backend.source = IntegrationSource.SALESFORCE
    handle = BaseHandle(mock_crm_backend)

    assert handle._backend == mock_crm_backend
    assert handle.source == IntegrationSource.SALESFORCE


def test_crm_handle_initialization(mock_crm_backend):
    mock_crm_backend.source = IntegrationSource.SALESFORCE
    handle = CRMHandle(mock_crm_backend)

    assert handle._backend == mock_crm_backend
    assert handle.source == IntegrationSource.SALESFORCE


@pytest.mark.anyio
async def test_crm_handle_get_opportunity(mock_crm_backend):
    handle = CRMHandle(mock_crm_backend)

    result = await handle.get_opportunity("123")

    assert result == {"id": "123", "name": "Test Opp"}


@pytest.mark.anyio
async def test_crm_handle_update_opportunity(mock_crm_backend):
    fields = {"name": "Updated Opp"}
    handle = CRMHandle(mock_crm_backend)

    result = await handle.update_opportunity("123", fields)

    assert result == {"id": "123", "name": "Updated Opp"}


@pytest.mark.anyio
async def test_crm_handle_list_opportunities_by_account(mock_crm_backend):
    handle = CRMHandle(mock_crm_backend)

    result = await handle.list_opportunities_by_account("acc123", limit=50, offset=10)

    assert result == [{"id": "123"}, {"id": "456"}]


@pytest.mark.anyio
async def test_crm_handle_list_opportunities_by_account_defaults(mock_crm_backend):
    handle = CRMHandle(mock_crm_backend)

    await handle.list_opportunities_by_account("acc123")


@pytest.mark.anyio
async def test_crm_handle_search_opportunities(mock_crm_backend):
    search_criteria = {"Name": "Big Deal"}
    handle = CRMHandle(mock_crm_backend)

    result = await handle.search_opportunities(search_criteria, limit=50, offset=10)

    assert result == [{"id": "opp123", "Name": "Big Deal"}]


@pytest.mark.anyio
async def test_crm_handle_search_opportunities_defaults(mock_crm_backend):
    search_criteria = {"Name": "Big Deal"}
    handle = CRMHandle(mock_crm_backend)

    await handle.search_opportunities(search_criteria)


@pytest.mark.anyio
async def test_crm_handle_get_account(mock_crm_backend):
    handle = CRMHandle(mock_crm_backend)

    result = await handle.get_account("acc123")

    assert result == {"id": "acc123", "name": "Test Account"}


@pytest.mark.anyio
async def test_crm_handle_update_account(mock_crm_backend):
    fields = {"name": "Updated Account"}
    handle = CRMHandle(mock_crm_backend)

    result = await handle.update_account("acc123", fields)

    assert result == {"id": "acc123", "name": "Updated Account"}


@pytest.mark.anyio
async def test_crm_handle_search_accounts(mock_crm_backend):
    search_criteria = {"Name": "Tech Corp"}
    handle = CRMHandle(mock_crm_backend)

    result = await handle.search_accounts(search_criteria, limit=50, offset=10)

    assert result == [{"id": "acc123", "Name": "Tech Corp"}]


@pytest.mark.anyio
async def test_crm_handle_search_accounts_defaults(mock_crm_backend):
    search_criteria = {"Name": "Tech Corp"}
    handle = CRMHandle(mock_crm_backend)

    await handle.search_accounts(search_criteria)


@pytest.mark.anyio
async def test_crm_handle_get_contact(mock_crm_backend):
    handle = CRMHandle(mock_crm_backend)

    result = await handle.get_contact("con123")

    assert result == {"id": "con123", "name": "Test Contact"}


@pytest.mark.anyio
async def test_crm_handle_create_contact(mock_crm_backend):
    contact_data = {"FirstName": "John", "LastName": "Doe"}
    handle = CRMHandle(mock_crm_backend)

    result = await handle.create_contact(contact_data)

    assert result == {"id": "con123", "FirstName": "John", "LastName": "Doe"}


@pytest.mark.anyio
async def test_crm_handle_update_contact(mock_crm_backend):
    contact_data = {"FirstName": "Jane"}
    handle = CRMHandle(mock_crm_backend)

    result = await handle.update_contact("con123", contact_data)

    assert result == {"id": "con123", "FirstName": "Jane"}


@pytest.mark.anyio
async def test_crm_handle_list_contacts_by_account(mock_crm_backend):
    handle = CRMHandle(mock_crm_backend)

    result = await handle.list_contacts_by_account("acc123", limit=50, offset=10)

    assert result == [{"id": "con123"}, {"id": "con456"}]


@pytest.mark.anyio
async def test_crm_handle_list_contacts_by_account_defaults(mock_crm_backend):
    handle = CRMHandle(mock_crm_backend)

    await handle.list_contacts_by_account("acc123")


@pytest.mark.anyio
async def test_crm_handle_search_contacts(mock_crm_backend):
    search_criteria = {"Email": "<EMAIL>"}
    handle = CRMHandle(mock_crm_backend)

    result = await handle.search_contacts(search_criteria, limit=50, offset=10)

    assert result == [{"id": "con123", "Email": "<EMAIL>"}]


@pytest.mark.anyio
async def test_crm_handle_search_contacts_defaults(mock_crm_backend):
    search_criteria = {"Email": "<EMAIL>"}
    handle = CRMHandle(mock_crm_backend)

    await handle.search_contacts(search_criteria)


@pytest.mark.anyio
async def test_crm_handle_get_task(mock_crm_backend):
    handle = CRMHandle(mock_crm_backend)

    result = await handle.get_task("tsk123")

    assert result == {"id": "tsk123", "subject": "Test Task"}


@pytest.mark.anyio
async def test_crm_handle_create_task(mock_crm_backend):
    task_data = {"Subject": "New Task", "Status": "Not Started"}
    handle = CRMHandle(mock_crm_backend)

    result = await handle.create_task(task_data)

    assert result == {"id": "tsk123", "Subject": "New Task", "Status": "Not Started"}


@pytest.mark.anyio
async def test_crm_handle_update_task(mock_crm_backend):
    task_data = {"Status": "Completed"}
    handle = CRMHandle(mock_crm_backend)

    result = await handle.update_task("tsk123", task_data)

    assert result == {"id": "tsk123", "Status": "Completed"}


@pytest.mark.anyio
async def test_crm_handle_list_tasks_by_contact(mock_crm_backend):
    handle = CRMHandle(mock_crm_backend)

    result = await handle.list_tasks_by_contact("con123", limit=50, offset=10)

    assert result == [{"id": "tsk123"}, {"id": "tsk456"}]


@pytest.mark.anyio
async def test_crm_handle_list_tasks_by_contact_defaults(mock_crm_backend):
    handle = CRMHandle(mock_crm_backend)

    await handle.list_tasks_by_contact("con123")


@pytest.mark.anyio
async def test_crm_handle_list_tasks_by_account(mock_crm_backend):
    handle = CRMHandle(mock_crm_backend)

    result = await handle.list_tasks_by_account("acc123", limit=50, offset=10)

    assert result == [{"id": "tsk123"}, {"id": "tsk456"}]


@pytest.mark.anyio
async def test_crm_handle_list_tasks_by_account_defaults(mock_crm_backend):
    handle = CRMHandle(mock_crm_backend)

    await handle.list_tasks_by_account("acc123")


@pytest.mark.anyio
async def test_crm_handle_get_event(mock_crm_backend):
    handle = CRMHandle(mock_crm_backend)

    result = await handle.get_event("evt123")

    assert result == {"id": "evt123", "subject": "Test Event"}


@pytest.mark.anyio
async def test_crm_handle_create_event(mock_crm_backend):
    event_data = {"Subject": "New Event", "StartDateTime": "2024-01-15T10:00:00Z"}
    handle = CRMHandle(mock_crm_backend)

    result = await handle.create_event(event_data)

    assert result == {
        "id": "evt123",
        "Subject": "New Event",
        "StartDateTime": "2024-01-15T10:00:00Z",
    }


@pytest.mark.anyio
async def test_crm_handle_update_event(mock_crm_backend):
    event_data = {"Subject": "Updated Event"}
    handle = CRMHandle(mock_crm_backend)

    result = await handle.update_event("evt123", event_data)

    assert result == {"id": "evt123", "Subject": "Updated Event"}


@pytest.mark.anyio
async def test_crm_handle_list_events_by_contact(mock_crm_backend):
    handle = CRMHandle(mock_crm_backend)

    result = await handle.list_events_by_contact("con123", limit=50, offset=10)

    assert result == [{"id": "evt123"}, {"id": "evt456"}]


@pytest.mark.anyio
async def test_crm_handle_list_events_by_contact_defaults(mock_crm_backend):
    handle = CRMHandle(mock_crm_backend)

    await handle.list_events_by_contact("con123")


@pytest.mark.anyio
async def test_crm_handle_list_events_by_account(mock_crm_backend):
    handle = CRMHandle(mock_crm_backend)

    result = await handle.list_events_by_account("acc123", limit=50, offset=10)

    assert result == [{"id": "evt123"}, {"id": "evt456"}]


@pytest.mark.anyio
async def test_crm_handle_list_events_by_account_defaults(mock_crm_backend):
    handle = CRMHandle(mock_crm_backend)

    await handle.list_events_by_account("acc123")


@pytest.mark.anyio
async def test_crm_handle_list_account_access(mock_crm_backend):
    handle = CRMHandle(mock_crm_backend)

    result = await handle.list_account_access("user123", limit=50, offset=10)

    assert result == [{"account_id": "acc123", "access_type": "read"}]


@pytest.mark.anyio
async def test_crm_handle_list_account_access_defaults(mock_crm_backend):
    handle = CRMHandle(mock_crm_backend)

    await handle.list_account_access("user123")


@pytest.mark.anyio
async def test_crm_handle_bulk_sync_account_access(mock_crm_backend):
    user_ids = ["user1", "user2"]
    handle = CRMHandle(mock_crm_backend)

    result = await handle.bulk_sync_account_access(
        user_ids, get_credentials_resolver=None, interval_seconds=600, daemon_mode=True
    )

    assert result == {"status": "started", "job_id": "job123"}


@pytest.mark.anyio
async def test_crm_handle_bulk_sync_account_access_defaults(mock_crm_backend):
    user_ids = ["user1", "user2"]
    handle = CRMHandle(mock_crm_backend)

    await handle.bulk_sync_account_access(user_ids)


@pytest.mark.anyio
async def test_crm_handle_store_account_summary(mock_crm_backend):
    handle = CRMHandle(mock_crm_backend)

    await handle.store_account_summary("user123", "acc123", "summary")


@pytest.mark.anyio
async def test_crm_handle_get_metrics(mock_crm_backend):
    handle = CRMHandle(mock_crm_backend)

    result = await handle.get_metrics("user123")

    expected_metrics = CRMMetrics(
        quota=1000000,
        closed_won=250000,
        pipeline=500000,
        forecast=300000,
    )
    assert result == expected_metrics


def test_messaging_handle_initialization(mock_messaging_backend):
    mock_messaging_backend.source = IntegrationSource.SLACK
    handle = MessagingHandle(mock_messaging_backend)

    assert handle._backend == mock_messaging_backend
    assert handle.source == IntegrationSource.SLACK


@pytest.mark.anyio
async def test_messaging_handle_search_channel_messages(mock_messaging_backend):
    expected_messages = [("doc1", 0.9), ("doc2", 0.8)]
    mock_messaging_backend.search_channel_messages.return_value = expected_messages
    handle = MessagingHandle(mock_messaging_backend)

    result = await handle.search_channel_messages("channel123", "test query", limit=5)

    mock_messaging_backend.search_channel_messages.assert_called_once_with(
        "channel123", "test query", 5
    )
    assert result == expected_messages


@pytest.mark.anyio
async def test_messaging_handle_search_channel_messages_defaults(
    mock_messaging_backend,
):
    handle = MessagingHandle(mock_messaging_backend)

    await handle.search_channel_messages("channel123", "test query")

    mock_messaging_backend.search_channel_messages.assert_called_once_with(
        "channel123", "test query", 10
    )


@pytest.mark.anyio
async def test_messaging_handle_start_channel_ingestion(mocker, mock_messaging_backend):
    channel_ids = ["channel1", "channel2"]
    expected_result = {"status": "started", "job_id": "ingestion123"}
    mock_messaging_backend.start_channel_ingestion = mocker.AsyncMock(
        return_value=expected_result
    )
    handle = MessagingHandle(mock_messaging_backend)

    result = await handle.start_channel_ingestion(
        channel_ids,
        interval_seconds=600,
        lookback_days=14,
        batch_size=200,
        daemon_mode=True,
    )

    mock_messaging_backend.start_channel_ingestion.assert_called_once_with(
        channel_ids, 600, 14, 200, True
    )
    assert result == expected_result


@pytest.mark.anyio
async def test_messaging_handle_start_channel_ingestion_defaults(
    mock_messaging_backend,
):
    channel_ids = ["channel1", "channel2"]
    handle = MessagingHandle(mock_messaging_backend)

    await handle.start_channel_ingestion(channel_ids)

    mock_messaging_backend.start_channel_ingestion.assert_called_once_with(
        channel_ids, 300, 7, 100, False
    )


@pytest.mark.anyio
async def test_messaging_handle_start_channel_processing(
    mocker, mock_messaging_backend
):
    channel_ids = ["channel1", "channel2"]
    expected_result = {"status": "started", "job_id": "processing123"}
    mock_messaging_backend.start_channel_processing = mocker.AsyncMock(
        return_value=expected_result
    )
    handle = MessagingHandle(mock_messaging_backend)

    result = await handle.start_channel_processing(
        channel_ids,
        interval_seconds=600,
        batch_size=200,
        daemon_mode=True,
    )

    mock_messaging_backend.start_channel_processing.assert_called_once_with(
        channel_ids, 600, 200, True
    )
    assert result == expected_result


@pytest.mark.anyio
async def test_messaging_handle_start_channel_processing_defaults(
    mock_messaging_backend,
):
    channel_ids = ["channel1", "channel2"]
    handle = MessagingHandle(mock_messaging_backend)

    await handle.start_channel_processing(channel_ids)

    mock_messaging_backend.start_channel_processing.assert_called_once_with(
        channel_ids,
        300,
        100,
        False,
    )


def test_file_handle_initialization(mock_file_backend):
    mock_file_backend.source = IntegrationSource.GCS
    handle = FileHandle(mock_file_backend)

    assert handle._backend == mock_file_backend
    assert handle.source == IntegrationSource.GCS


@pytest.mark.anyio
async def test_file_handle_start_processing(mocker, mock_file_backend):
    bucket_names = ["bucket1", "bucket2"]
    expected_result = {"status": "started", "job_id": "processing123"}
    mock_file_backend.start_processing = mocker.AsyncMock(return_value=expected_result)
    handle = FileHandle(mock_file_backend)

    result = await handle.start_processing(
        bucket_names,
    )

    mock_file_backend.start_processing.assert_called_once_with(
        bucket_names,
    )
    assert result == expected_result


@pytest.mark.anyio
async def test_file_handle_search_files(mocker, mock_file_backend):
    query = "test query"
    expected_result = [("doc1", 0.9), ("doc2", 0.8)]
    mock_file_backend.search_files = mocker.AsyncMock(return_value=expected_result)
    handle = FileHandle(mock_file_backend)

    result = await handle.search_files(query, limit=5)

    mock_file_backend.search_files.assert_called_once_with(query, 5)
    assert result == expected_result


@pytest.mark.anyio
async def test_file_handle_search_files_defaults(mock_file_backend):
    query = "test query"
    handle = FileHandle(mock_file_backend)

    await handle.search_files(query)

    mock_file_backend.search_files.assert_called_once_with(query, 10)


@pytest.mark.anyio
async def test_file_handle_list_files(mocker, mock_file_backend):
    from datetime import datetime

    from app.integrations.schemas import FileData

    container_name = "test-container"
    expected_files = [
        FileData(
            id="file1",
            name="document.pdf",
            size=1024,
            time_created=datetime(2024, 1, 1, 12, 0, 0),
            last_modified=datetime(2024, 1, 2, 12, 0, 0),
            md5_hash="hash1",
            content_type="application/pdf",
        )
    ]
    mock_file_backend.list_files = mocker.AsyncMock(return_value=expected_files)
    handle = FileHandle(mock_file_backend)

    result = await handle.list_files(container_name)

    mock_file_backend.list_files.assert_called_once_with(container_name)
    assert result == expected_files


@pytest.mark.anyio
async def test_file_handle_create_bucket(mocker, mock_file_backend):
    bucket_name = "test-bucket"
    mock_file_backend.create_bucket = mocker.AsyncMock()
    handle = FileHandle(mock_file_backend)

    await handle.create_bucket(bucket_name)

    mock_file_backend.create_bucket.assert_called_once_with(bucket_name)


@pytest.mark.anyio
async def test_file_handle_bucket_exists_true(mocker, mock_file_backend):
    bucket_name = "existing-bucket"
    mock_file_backend.bucket_exists = mocker.AsyncMock(return_value=True)
    handle = FileHandle(mock_file_backend)

    result = await handle.bucket_exists(bucket_name)

    mock_file_backend.bucket_exists.assert_called_once_with(bucket_name)
    assert result is True


@pytest.mark.anyio
async def test_file_handle_bucket_exists_false(mocker, mock_file_backend):
    bucket_name = "nonexistent-bucket"
    mock_file_backend.bucket_exists = mocker.AsyncMock(return_value=False)
    handle = FileHandle(mock_file_backend)

    result = await handle.bucket_exists(bucket_name)

    mock_file_backend.bucket_exists.assert_called_once_with(bucket_name)
    assert result is False


@pytest.mark.anyio
async def test_file_handle_delete_file(mocker, mock_file_backend):
    container_name = "test-container"
    file_name = "test-file.txt"
    mock_file_backend.delete_file = mocker.AsyncMock()
    handle = FileHandle(mock_file_backend)

    await handle.delete_file(container_name, file_name)

    mock_file_backend.delete_file.assert_called_once_with(container_name, file_name)


@pytest.mark.anyio
async def test_file_handle_upload_file(mocker, mock_file_backend):
    from io import BytesIO

    container_name = "test-container"
    file_obj = BytesIO(b"test content")
    file_name = "test-file.txt"
    mock_file_backend.upload_file = mocker.AsyncMock()
    handle = FileHandle(mock_file_backend)

    await handle.upload_file(container_name, file_obj, file_name)

    mock_file_backend.upload_file.assert_called_once_with(
        container_name, file_obj, file_name, None
    )


@pytest.fixture
def mock_calendar_backend(mocker):
    backend = mocker.Mock(spec=BaseCalendarBackend)

    async def mock_list_calendars(*_args, **_kwargs):
        return [
            {
                "id": "cal1",
                "name": "Primary Calendar",
                "description": "Main calendar",
                "timezone": "America/New_York",
                "is_primary": True,
                "access_role": "owner",
            },
            {
                "id": "cal2",
                "name": "Work Calendar",
                "description": "Work events",
                "timezone": "America/New_York",
                "is_primary": False,
                "access_role": "writer",
            },
        ]

    async def mock_get_calendar(*_args, **_kwargs):
        return {
            "id": "cal1",
            "name": "Primary Calendar",
            "description": "Main calendar",
            "timezone": "America/New_York",
            "is_primary": True,
            "access_role": "owner",
        }

    async def mock_get_event(*_args, **_kwargs):
        return {
            "id": "evt1",
            "calendar_id": "cal1",
            "title": "Test Event",
            "description": "Test event description",
            "start": {
                "date_time": datetime(2024, 1, 15, 10, 0, 0),
                "timezone": "America/New_York",
            },
            "end": {
                "date_time": datetime(2024, 1, 15, 11, 0, 0),
                "timezone": "America/New_York",
            },
        }

    async def mock_list_events(*_args, **_kwargs):
        return {
            "items": [
                {
                    "id": "evt1",
                    "summary": "Test Event",
                    "start": {"dateTime": "2024-01-15T10:00:00-05:00"},
                    "end": {"dateTime": "2024-01-15T11:00:00-05:00"},
                },
                {
                    "id": "evt2",
                    "summary": "Another Event",
                    "start": {"dateTime": "2024-01-15T14:00:00-05:00"},
                    "end": {"dateTime": "2024-01-15T15:00:00-05:00"},
                },
            ],
            "nextPageToken": None,
        }

    async def mock_search_events(*_args, **_kwargs):
        return [
            {
                "id": "evt1",
                "calendar_id": "cal1",
                "title": "Meeting with client",
                "description": "Important client meeting",
                "start": {
                    "date_time": datetime(2024, 1, 15, 10, 0, 0),
                    "timezone": "America/New_York",
                },
                "end": {
                    "date_time": datetime(2024, 1, 15, 11, 0, 0),
                    "timezone": "America/New_York",
                },
            }
        ]

    async def mock_get_user_info(*_args, **_kwargs):
        return {
            "id": "user123",
            "email": "<EMAIL>",
            "name": "Test User",
        }

    # Assign the async mock functions
    backend.list_calendars = mock_list_calendars
    backend.get_calendar = mock_get_calendar
    backend.get_event = mock_get_event
    backend.list_events = mock_list_events
    backend.search_events = mock_search_events
    backend.get_user_info = mock_get_user_info

    return backend


def test_calendar_handle_initialization(mock_calendar_backend):
    mock_calendar_backend.source = IntegrationSource.GOOGLE_CALENDAR
    handle = CalendarHandle(mock_calendar_backend)

    assert handle._backend == mock_calendar_backend
    assert handle.source == IntegrationSource.GOOGLE_CALENDAR


@pytest.mark.anyio
async def test_calendar_handle_list_calendars(mock_calendar_backend):
    handle = CalendarHandle(mock_calendar_backend)

    result = await handle.list_calendars()

    assert len(result) == 2
    assert result[0]["id"] == "cal1"
    assert result[0]["name"] == "Primary Calendar"
    assert result[0]["is_primary"] is True
    assert result[1]["id"] == "cal2"
    assert result[1]["name"] == "Work Calendar"
    assert result[1]["is_primary"] is False


@pytest.mark.anyio
async def test_calendar_handle_get_calendar(mock_calendar_backend):
    handle = CalendarHandle(mock_calendar_backend)

    result = await handle.get_calendar("cal1")

    assert result["id"] == "cal1"
    assert result["name"] == "Primary Calendar"
    assert result["is_primary"] is True


@pytest.mark.anyio
async def test_calendar_handle_get_event(mock_calendar_backend):
    handle = CalendarHandle(mock_calendar_backend)

    result = await handle.get_event("cal1", "evt1")

    assert result["id"] == "evt1"
    assert result["calendar_id"] == "cal1"
    assert result["title"] == "Test Event"
    assert result["description"] == "Test event description"


@pytest.mark.anyio
async def test_calendar_handle_list_events(mock_calendar_backend):
    handle = CalendarHandle(mock_calendar_backend)

    result = await handle.list_events("cal1")

    assert "items" in result
    assert len(result["items"]) == 2
    assert result["items"][0]["id"] == "evt1"
    assert result["items"][0]["summary"] == "Test Event"


@pytest.mark.anyio
async def test_calendar_handle_list_events_with_params(mock_calendar_backend):
    handle = CalendarHandle(mock_calendar_backend)
    start_time = datetime(2024, 1, 15, 9, 0, 0)
    end_time = datetime(2024, 1, 15, 17, 0, 0)

    result = await handle.list_events(
        "cal1",
        start_time=start_time,
        end_time=end_time,
        max_results=10,
        single_events=True,
        order_by="startTime",
        show_deleted=False,
    )

    assert "items" in result
    assert len(result["items"]) == 2


@pytest.mark.anyio
async def test_calendar_handle_search_events(mock_calendar_backend):
    handle = CalendarHandle(mock_calendar_backend)

    result = await handle.search_events("cal1", "meeting")

    assert len(result) == 1
    assert result[0]["id"] == "evt1"
    assert result[0]["title"] == "Meeting with client"


@pytest.mark.anyio
async def test_calendar_handle_search_events_with_params(mock_calendar_backend):
    handle = CalendarHandle(mock_calendar_backend)
    start_time = datetime(2024, 1, 15, 9, 0, 0)
    end_time = datetime(2024, 1, 15, 17, 0, 0)

    result = await handle.search_events(
        "cal1",
        "meeting",
        start_time=start_time,
        end_time=end_time,
        max_results=10,
        order_by="startTime",
    )

    assert len(result) == 1
    assert result[0]["title"] == "Meeting with client"


@pytest.mark.anyio
async def test_calendar_handle_get_user_info(mock_calendar_backend):
    handle = CalendarHandle(mock_calendar_backend)

    result = await handle.get_user_info()

    assert result["id"] == "user123"
    assert result["email"] == "<EMAIL>"
    assert result["name"] == "Test User"
