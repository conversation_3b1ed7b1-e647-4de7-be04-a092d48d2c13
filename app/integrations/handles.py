from collections.abc import Callable
from datetime import datetime
from typing import Any

from app.integrations.base.calendar_backend import BaseCalendarBackend
from app.integrations.base.credentials_resolver import ICredentialsResolver
from app.integrations.base.crm_backend import BaseCRMBackend
from app.integrations.base.email_backend import BaseEmailBackend
from app.integrations.base.file_backend import BaseFileBackend
from app.integrations.base.messaging_backend import BaseMessagingBackend
from app.integrations.schemas import (
    CRMMetrics,
    DocumentData,
    EmailMessage,
    EmailThread,
    FileData,
)
from app.integrations.types import IntegrationSource


class BaseHandle:
    def __init__(self, backend):
        self._backend = backend

    @property
    def source(self) -> IntegrationSource:
        return self._backend.source


class CRMHandle(BaseHandle):
    def __init__(self, backend: BaseCRMBackend):
        super().__init__(backend)

    # Core CRM operations
    async def get_opportunity(self, opportunity_id: str) -> dict[str, Any]:
        return await self._backend.get_opportunity(opportunity_id)

    async def update_opportunity(
        self, opportunity_id: str, fields: dict[str, Any]
    ) -> dict[str, Any]:
        return await self._backend.update_opportunity(opportunity_id, fields)

    async def list_opportunities_by_account(
        self, account_id: str, limit: int = 100, offset: int = 0
    ) -> list[dict[str, Any]]:
        return await self._backend.list_opportunities_by_account(
            account_id, limit, offset
        )

    async def search_opportunities(
        self,
        search_criteria: dict[str, Any],
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        return await self._backend.search_opportunities(search_criteria, limit, offset)

    async def get_account(self, account_id: str) -> dict[str, Any]:
        return await self._backend.get_account(account_id)

    async def update_account(
        self, account_id: str, fields: dict[str, Any]
    ) -> dict[str, Any]:
        return await self._backend.update_account(account_id, fields)

    async def search_accounts(
        self,
        search_criteria: dict[str, Any],
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        return await self._backend.search_accounts(search_criteria, limit, offset)

    async def get_contact(self, contact_id: str) -> dict[str, Any]:
        return await self._backend.get_contact(contact_id)

    async def create_contact(self, contact_data: dict[str, Any]) -> dict[str, Any]:
        return await self._backend.create_contact(contact_data)

    async def update_contact(
        self, contact_id: str, contact_data: dict[str, Any]
    ) -> dict[str, Any]:
        return await self._backend.update_contact(contact_id, contact_data)

    async def list_contacts_by_account(
        self, account_id: str, limit: int = 100, offset: int = 0
    ) -> list[dict[str, Any]]:
        return await self._backend.list_contacts_by_account(account_id, limit, offset)

    async def search_contacts(
        self,
        search_criteria: dict[str, Any],
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        return await self._backend.search_contacts(search_criteria, limit, offset)

    async def get_task(self, task_id: str) -> dict[str, Any]:
        return await self._backend.get_task(task_id)

    async def create_task(self, task_data: dict[str, Any]) -> dict[str, Any]:
        return await self._backend.create_task(task_data)

    async def update_task(
        self, task_id: str, task_data: dict[str, Any]
    ) -> dict[str, Any]:
        return await self._backend.update_task(task_id, task_data)

    async def list_tasks_by_contact(
        self, contact_id: str, limit: int = 100, offset: int = 0
    ) -> list[dict[str, Any]]:
        return await self._backend.list_tasks_by_contact(contact_id, limit, offset)

    async def list_tasks_by_account(
        self, account_id: str, limit: int = 100, offset: int = 0
    ) -> list[dict[str, Any]]:
        return await self._backend.list_tasks_by_account(account_id, limit, offset)

    async def list_tasks_by_opportunity(
        self, opportunity_id: str, limit: int = 100, offset: int = 0
    ) -> list[dict[str, Any]]:
        return await self._backend.list_tasks_by_opportunity(
            opportunity_id, limit, offset
        )

    async def get_event(self, event_id: str) -> dict[str, Any]:
        return await self._backend.get_event(event_id)

    async def create_event(self, event_data: dict[str, Any]) -> dict[str, Any]:
        return await self._backend.create_event(event_data)

    async def update_event(
        self, event_id: str, event_data: dict[str, Any]
    ) -> dict[str, Any]:
        return await self._backend.update_event(event_id, event_data)

    async def list_events_by_contact(
        self, contact_id: str, limit: int = 100, offset: int = 0
    ) -> list[dict[str, Any]]:
        return await self._backend.list_events_by_contact(contact_id, limit, offset)

    async def list_events_by_account(
        self, account_id: str, limit: int = 100, offset: int = 0
    ) -> list[dict[str, Any]]:
        return await self._backend.list_events_by_account(account_id, limit, offset)

    async def list_events_by_opportunity(
        self, opportunity_id: str, limit: int = 100, offset: int = 0
    ) -> list[dict[str, Any]]:
        return await self._backend.list_events_by_opportunity(
            opportunity_id, limit, offset
        )

    async def list_account_access(
        self, crm_user_id: str, limit: int = 100, offset: int = 0
    ) -> list[dict[str, Any]]:
        return await self._backend.list_account_access(crm_user_id, limit, offset)

    async def get_metrics(
        self, crm_user_id: str, field_mapping: dict[str, Any] | None = None
    ) -> CRMMetrics:
        return await self._backend.get_metrics(crm_user_id, field_mapping)

    # Sync operations
    async def bulk_sync_account_access(
        self,
        crm_user_ids: list[str],
        get_credentials_resolver: Callable[[str], ICredentialsResolver] | None = None,
        interval_seconds: int = 300,
        daemon_mode: bool = False,
    ) -> dict[str, Any]:
        return await self._backend.bulk_sync_account_access(
            crm_user_ids, get_credentials_resolver, interval_seconds, daemon_mode
        )

    async def store_account_summary(
        self, user_id: str, crm_account_id: str, summary: str
    ) -> None:
        return await self._backend.store_account_summary(
            user_id, crm_account_id, summary
        )


class MessagingHandle(BaseHandle):
    def __init__(self, backend: BaseMessagingBackend):
        super().__init__(backend)

    async def search_channel_messages(
        self, channel_id: str, query: str, limit: int = 10
    ) -> list[tuple[DocumentData, float]]:
        return await self._backend.search_channel_messages(channel_id, query, limit)

    async def start_channel_ingestion(
        self,
        channel_ids: list[str],
        interval_seconds: int = 300,
        lookback_days: int = 7,
        batch_size: int = 100,
        daemon_mode: bool = False,
    ) -> dict[str, Any]:
        return await self._backend.start_channel_ingestion(
            channel_ids,
            interval_seconds,
            lookback_days,
            batch_size,
            daemon_mode,
        )

    async def start_channel_processing(
        self,
        channel_ids: list[str],
        interval_seconds: int = 300,
        batch_size: int = 100,
        daemon_mode: bool = False,
    ) -> dict[str, Any]:
        return await self._backend.start_channel_processing(
            channel_ids, interval_seconds, batch_size, daemon_mode
        )


class FileHandle(BaseHandle):
    def __init__(self, backend: BaseFileBackend):
        super().__init__(backend)

    async def start_processing(self, bucket_names: list[str]) -> dict[str, Any]:
        return await self._backend.start_processing(bucket_names)

    async def search_files(
        self,
        query: str,
        limit: int = 10,
    ) -> list[tuple[DocumentData, float]]:
        return await self._backend.search_files(query, limit)

    async def list_files(
        self,
        container_name: str,
    ) -> list[FileData]:
        return await self._backend.list_files(container_name)

    async def create_bucket(self, bucket_name: str) -> None:
        return await self._backend.create_bucket(bucket_name)

    async def bucket_exists(self, bucket_name: str) -> bool:
        return await self._backend.bucket_exists(bucket_name)

    async def delete_file(self, container_name: str, file_name: str) -> None:
        return await self._backend.delete_file(container_name, file_name)

    async def upload_file(
        self,
        container_name: str,
        file_obj,
        file_name: str,
        content_type: str | None = None,
    ) -> None:
        return await self._backend.upload_file(
            container_name, file_obj, file_name, content_type
        )


class CalendarHandle(BaseHandle):
    def __init__(self, backend: BaseCalendarBackend):
        super().__init__(backend)

    async def list_calendars(self) -> list[dict[str, Any]]:
        return await self._backend.list_calendars()

    async def get_calendar(self, calendar_id: str) -> dict[str, Any]:
        return await self._backend.get_calendar(calendar_id)

    async def get_event(self, calendar_id: str, event_id: str) -> dict[str, Any]:
        return await self._backend.get_event(calendar_id, event_id)

    async def list_events(
        self,
        calendar_id: str,
        start_time: datetime | None = None,
        end_time: datetime | None = None,
        max_results: int = 250,
        single_events: bool = True,
        order_by: str = "startTime",
        show_deleted: bool = False,
        page_token: str | None = None,
    ) -> dict[str, Any]:
        return await self._backend.list_events(
            calendar_id,
            start_time,
            end_time,
            max_results,
            single_events,
            order_by,
            show_deleted,
            page_token,
        )

    async def search_events(
        self,
        calendar_id: str,
        query: str,
        start_time: datetime | None = None,
        end_time: datetime | None = None,
        max_results: int = 250,
        order_by: str = "startTime",
    ) -> list[dict[str, Any]]:
        return await self._backend.search_events(
            calendar_id, query, start_time, end_time, max_results, order_by
        )

    async def get_user_info(self) -> dict[str, Any]:
        return await self._backend.get_user_info()


class EmailHandle(BaseHandle):
    def __init__(self, backend: BaseEmailBackend):
        super().__init__(backend)

    async def get_user_info(self) -> dict[str, Any]:
        return await self._backend.get_user_info()

    async def list_messages(
        self,
        query: str = "",
        max_results: int = 100,
        page_token: str | None = None,
        include_spam_trash: bool = False,
    ) -> list[EmailMessage]:
        return await self._backend.list_messages(
            query=query,
            max_results=max_results,
            page_token=page_token,
            include_spam_trash=include_spam_trash,
        )

    async def get_message(self, message_id: str) -> EmailMessage:
        return await self._backend.get_message(message_id)

    async def list_threads(
        self,
        query: str = "",
        max_results: int = 100,
        page_token: str | None = None,
        include_spam_trash: bool = False,
    ) -> list[EmailThread]:
        return await self._backend.list_threads(
            query=query,
            max_results=max_results,
            page_token=page_token,
            include_spam_trash=include_spam_trash,
        )

    async def get_thread(self, thread_id: str) -> EmailThread:
        return await self._backend.get_thread(thread_id)

    async def search_messages(
        self,
        sender: str | None = None,
        recipient: str | None = None,
        subject: str | None = None,
        has_attachment: bool | None = None,
        is_unread: bool | None = None,
        in_folder: str | None = None,
        after_date: datetime | None = None,
        before_date: datetime | None = None,
        query_text: str | None = None,
        max_results: int = 100,
    ) -> list[EmailMessage]:
        return await self._backend.search_messages(
            sender=sender,
            recipient=recipient,
            subject=subject,
            has_attachment=has_attachment,
            is_unread=is_unread,
            in_folder=in_folder,
            after_date=after_date,
            before_date=before_date,
            query_text=query_text,
            max_results=max_results,
        )

    async def modify_message(
        self,
        message_id: str,
        add_labels: list[str] | None = None,
        remove_labels: list[str] | None = None,
    ) -> dict[str, Any]:
        return await self._backend.modify_message(
            message_id=message_id,
            add_labels=add_labels,
            remove_labels=remove_labels,
        )

    async def list_labels(self) -> list[dict[str, Any]]:
        return await self._backend.list_labels()

    # Additional methods that are called by email_tools.py
    async def list_filtered_labels(
        self, name_filter: str | None = None
    ) -> list[dict[str, Any]]:
        """List labels with optional name filtering."""
        labels = await self.list_labels()

        if name_filter:
            filter_lower = name_filter.lower()
            labels = [
                label
                for label in labels
                if filter_lower in label.get("name", "").lower()
            ]

        return labels

    async def list_threads_with_filters(
        self,
        query: str | None = None,
        company_filter: str | None = None,
        client_filter: str | None = None,
        unread_only: bool = False,
        max_results: int = 50,
        include_spam_trash: bool = False,
    ) -> list[EmailThread]:
        """List threads with business logic filters."""
        # Build Gmail query
        query_parts = []

        if query:
            query_parts.append(query)
        if company_filter:
            query_parts.append(f"({company_filter})")
        if client_filter:
            query_parts.append(f"({client_filter})")
        if unread_only:
            query_parts.append("is:unread")

        gmail_query = " ".join(query_parts) if query_parts else ""

        return await self.list_threads(
            query=gmail_query,
            max_results=max_results,
            include_spam_trash=include_spam_trash,
        )

    async def get_thread_with_limit(
        self, thread_id: str, max_messages: int | None = None
    ) -> EmailThread:
        """Get thread with optional message limit."""
        thread = await self.get_thread(thread_id)

        if max_messages and len(thread.messages) > max_messages:
            # Create a new thread with limited messages
            limited_thread = EmailThread(
                id=thread.id,
                snippet=thread.snippet,
                messages=thread.messages[:max_messages],
                labels=thread.labels,
                history_id=thread.history_id,
            )
            return limited_thread

        return thread

    async def search_emails_by_context(
        self,
        company_name: str | None = None,
        client_name: str | None = None,
        domain: str | None = None,
        subject_keywords: str | None = None,
        date_range_days: int | None = 30,
        max_results: int = 20,
    ) -> list[EmailMessage]:
        """Search emails by business context with intelligent query building."""
        from datetime import datetime, timedelta

        # Build search parameters
        sender: str | None = None
        subject: str | None = None
        after_date: datetime | None = None
        query_text: str | None = None

        if domain:
            sender = f"*@{domain}"

        if subject_keywords:
            subject = subject_keywords

        if date_range_days:
            after_date = datetime.now() - timedelta(days=date_range_days)

        # Build query text for company/client names
        query_parts = []
        if company_name:
            query_parts.append(f'"{company_name}"')
        if client_name:
            query_parts.append(f'"{client_name}"')

        if query_parts:
            query_text = " ".join(query_parts)

        return await self.search_messages(
            sender=sender,
            subject=subject,
            after_date=after_date,
            query_text=query_text,
            max_results=max_results,
        )
