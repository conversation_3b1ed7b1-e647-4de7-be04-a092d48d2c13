services:
  db:
    # Use a Postgres 16 image that already bundles pgvector
    image: pgvector/pgvector:pg16
    restart: always
    environment:
      POSTGRES_USER: pearl_user
      POSTGRES_PASSWORD: pearl_pass
      POSTGRES_DB: pearl
      TZ: UTC
    ports:
      - "5432:5432"
    volumes:
      - db_data:/var/lib/postgresql/data

  # app:
  #   image: python:3.12.8-slim
  #   working_dir: /app
  #   # mount your code so you can edit locally
  #   volumes:
  #     - .:/app:cached
  #   ports:
  #     - "8000:8000"
  #   depends_on:
  #     - db
  #   environment:
  #     # for consistency with Postgres
  #     TZ: UTC
  #   command: >
  #     bash -lc "
  #     pip install --upgrade pip &&
  #     pip install poetry pre-commit &&
  #     poetry config virtualenvs.create false &&
  #     poetry install --no-interaction &&
  #     pre-commit install --hook-type pre-push &&
  #     pre-commit install --hook-type pre-commit &&
  #     alembic upgrade head &&
  #     uvicorn main:app --reload --host 0.0.0.0 --port 8000
  #     "

volumes:
  db_data:
